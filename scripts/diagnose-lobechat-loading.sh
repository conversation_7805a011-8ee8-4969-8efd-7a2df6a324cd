#!/bin/bash

echo "🔍 LobeChat 加载问题诊断"
echo "================================"

echo "1. 检查 LobeChat 服务状态..."
LOBECHAT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:3210")
echo "   LobeChat 直接访问状态: HTTP $LOBECHAT_STATUS"

if [ "$LOBECHAT_STATUS" = "200" ]; then
    echo "   ✅ LobeChat 服务正常运行"
else
    echo "   ❌ LobeChat 服务异常"
    exit 1
fi

echo ""
echo "2. 检查代理访问状态..."
PROXY_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://*************/chat/" -k)
echo "   代理访问状态: HTTP $PROXY_STATUS"

if [ "$PROXY_STATUS" = "200" ]; then
    echo "   ✅ 代理访问正常"
else
    echo "   ❌ 代理访问异常"
fi

echo ""
echo "3. 检查关键 API 端点..."

# 检查认证 API
AUTH_API=$(curl -s "https://*************/chat/api/auth/session" -k)
echo "   认证 API 响应:"
echo "   $AUTH_API" | head -1

# 检查是否有其他 API
echo ""
echo "   尝试其他常见 API 端点:"

API_ENDPOINTS=(
    "/api/auth/providers"
    "/api/auth/csrf"
    "/api/models"
    "/api/chat"
    "/api/user"
)

for endpoint in "${API_ENDPOINTS[@]}"; do
    API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://*************/chat$endpoint" -k)
    echo "   $endpoint: HTTP $API_STATUS"
done

echo ""
echo "4. 检查 JavaScript 控制台错误..."

# 创建一个简单的 HTML 页面来测试 LobeChat 加载
cat > /tmp/lobechat-test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>LobeChat 加载测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
        #log { background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>LobeChat 加载诊断</h1>
    
    <div id="status" class="status info">正在加载 LobeChat...</div>
    
    <div>
        <h3>控制台日志:</h3>
        <div id="log"></div>
    </div>
    
    <div>
        <h3>LobeChat iframe:</h3>
        <iframe id="lobechat" src="/chat/"></iframe>
    </div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        const iframe = document.getElementById('lobechat');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        // 监听所有错误
        window.addEventListener('error', function(e) {
            addLog(`JavaScript 错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });
        
        // 监听未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', function(e) {
            addLog(`未处理的 Promise 拒绝: ${e.reason}`, 'error');
        });
        
        // 监听 iframe 加载事件
        iframe.onload = function() {
            addLog('iframe 加载完成', 'success');
            status.textContent = 'LobeChat iframe 已加载';
            status.className = 'status success';
            
            // 尝试检查 iframe 内容
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (iframeDoc) {
                    const title = iframeDoc.title;
                    addLog(`iframe 页面标题: ${title}`, 'info');
                    
                    // 检查是否有错误信息
                    const errorElements = iframeDoc.querySelectorAll('.error, .next-error-h1, [class*="error"]');
                    if (errorElements.length > 0) {
                        addLog(`发现 ${errorElements.length} 个错误元素`, 'error');
                    }
                    
                    // 检查是否有加载指示器
                    const loadingElements = iframeDoc.querySelectorAll('[class*="loading"], [class*="spinner"]');
                    if (loadingElements.length > 0) {
                        addLog(`发现 ${loadingElements.length} 个加载指示器`, 'info');
                    }
                } else {
                    addLog('无法访问 iframe 内容（跨域限制）', 'info');
                }
            } catch (e) {
                addLog(`检查 iframe 内容时出错: ${e.message}`, 'error');
            }
        };
        
        iframe.onerror = function() {
            addLog('iframe 加载失败', 'error');
            status.textContent = 'LobeChat iframe 加载失败';
            status.className = 'status error';
        };
        
        // 定期检查 iframe 状态
        let checkCount = 0;
        const checkInterval = setInterval(function() {
            checkCount++;
            addLog(`状态检查 #${checkCount}`, 'info');
            
            if (checkCount >= 30) { // 30秒后停止检查
                clearInterval(checkInterval);
                addLog('状态检查已停止', 'info');
            }
        }, 1000);
        
        addLog('诊断脚本已启动', 'success');
    </script>
</body>
</html>
EOF

echo "   已创建诊断页面: /tmp/lobechat-test.html"
echo "   可以通过浏览器访问此页面进行详细诊断"

echo ""
echo "5. 检查 LobeChat 配置..."

# 检查 LobeChat 是否需要特定配置
echo "   检查 LobeChat 是否需要环境变量配置..."

# 尝试获取 LobeChat 的配置信息
LOBECHAT_HTML=$(curl -s "http://*************:3210" | head -50)
if echo "$LOBECHAT_HTML" | grep -q "configuration"; then
    echo "   ⚠️  LobeChat 可能需要配置"
else
    echo "   ✅ LobeChat 基本配置正常"
fi

echo ""
echo "6. 建议的解决方案..."

echo "   基于诊断结果，建议尝试以下解决方案:"
echo ""
echo "   A. 如果 LobeChat 直接访问正常，但代理访问卡住:"
echo "      - 检查 nginx 配置是否正确"
echo "      - 检查 base 标签是否正确注入"
echo "      - 检查静态资源路径是否正确重写"
echo ""
echo "   B. 如果 LobeChat 需要配置:"
echo "      - 检查 LobeChat 的环境变量"
echo "      - 确保数据库连接正常（如果需要）"
echo "      - 检查认证配置"
echo ""
echo "   C. 如果是 JavaScript 执行问题:"
echo "      - 检查浏览器控制台错误"
echo "      - 确保所有静态资源都能正常加载"
echo "      - 检查 CSP 策略是否阻止了脚本执行"

echo ""
echo "🔧 快速测试命令:"
echo "   直接访问: curl -s 'http://*************:3210' | grep -i 'lobechat'"
echo "   代理访问: curl -s 'https://*************/chat/' -k | grep -i 'lobechat'"
echo "   认证测试: curl -s 'https://*************/chat/api/auth/session' -k"
