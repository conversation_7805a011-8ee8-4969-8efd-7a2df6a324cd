#!/bin/bash

echo "🔍 LobeChat 图标完整性验证"
echo "================================"

BASE_URL="https://192.168.60.52"

echo ""
echo "1. 检查浏览器可能请求的图标路径..."

# 常见的浏览器图标请求路径
BROWSER_ICON_PATHS=(
    "/favicon.ico"
    "/favicon.png"
    "/favicon.svg"
    "/favicon-16x16.png"
    "/favicon-32x32.png"
    "/favicon-96x96.png"
    "/apple-touch-icon.png"
    "/apple-touch-icon-57x57.png"
    "/apple-touch-icon-60x60.png"
    "/apple-touch-icon-72x72.png"
    "/apple-touch-icon-76x76.png"
    "/apple-touch-icon-114x114.png"
    "/apple-touch-icon-120x120.png"
    "/apple-touch-icon-144x144.png"
    "/apple-touch-icon-152x152.png"
    "/apple-touch-icon-180x180.png"
    "/android-chrome-192x192.png"
    "/android-chrome-512x512.png"
    "/mstile-150x150.png"
    "/safari-pinned-tab.svg"
)

SUCCESS_COUNT=0
TOTAL_COUNT=${#BROWSER_ICON_PATHS[@]}

for path in "${BROWSER_ICON_PATHS[@]}"; do
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$path" -k)
    if [ "$STATUS" = "200" ]; then
        echo "   ✅ $path (HTTP $STATUS)"
        ((SUCCESS_COUNT++))
    elif [ "$STATUS" = "404" ]; then
        echo "   ⚠️  $path (HTTP $STATUS - 文件不存在，这是正常的)"
    else
        echo "   ❌ $path (HTTP $STATUS)"
    fi
done

echo ""
echo "📊 图标文件统计: $SUCCESS_COUNT/$TOTAL_COUNT 个文件可访问"

echo ""
echo "2. 检查LobeChat页面中的图标引用..."

# 获取主页面内容
MAIN_PAGE=$(curl -s "$BASE_URL/chat/" -k)

# 检查页面中引用的图标
echo "   检查页面中的图标引用..."
ICON_REFS=$(echo "$MAIN_PAGE" | grep -o 'href="[^"]*\.\(ico\|png\|svg\)"' | head -10)
if [ -n "$ICON_REFS" ]; then
    echo "   ✅ 找到图标引用:"
    echo "$ICON_REFS" | sed 's/^/     /'
else
    echo "   ⚠️  未找到明显的图标引用"
fi

echo ""
echo "3. 检查manifest文件..."

MANIFEST_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/manifest.webmanifest" -k)
if [ "$MANIFEST_STATUS" = "200" ]; then
    echo "   ✅ manifest.webmanifest 可访问 (HTTP $MANIFEST_STATUS)"
    
    # 检查manifest内容
    MANIFEST_CONTENT=$(curl -s "$BASE_URL/manifest.webmanifest" -k)
    if echo "$MANIFEST_CONTENT" | grep -q "icons"; then
        echo "   ✅ manifest包含图标配置"
    else
        echo "   ⚠️  manifest不包含图标配置"
    fi
else
    echo "   ❌ manifest.webmanifest 不可访问 (HTTP $MANIFEST_STATUS)"
fi

echo ""
echo "4. 检查PWA相关图标..."

PWA_ICONS=(
    "/manifest.json"
    "/browserconfig.xml"
    "/site.webmanifest"
)

for icon in "${PWA_ICONS[@]}"; do
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$icon" -k)
    if [ "$STATUS" = "200" ]; then
        echo "   ✅ $icon (HTTP $STATUS)"
    else
        echo "   ⚠️  $icon (HTTP $STATUS)"
    fi
done

echo ""
echo "5. 检查图标页面的功能..."

# 检查/icons页面是否是一个功能页面
ICONS_PAGE=$(curl -s "$BASE_URL/icons" -k)
if echo "$ICONS_PAGE" | grep -q "LobeChat"; then
    echo "   ✅ /icons 页面是LobeChat的功能页面"
else
    echo "   ⚠️  /icons 页面内容异常"
fi

# 检查页面大小
ICONS_SIZE=$(echo "$ICONS_PAGE" | wc -c)
if [ "$ICONS_SIZE" -gt 10000 ]; then
    echo "   ✅ /icons 页面内容丰富 (${ICONS_SIZE} 字节)"
else
    echo "   ⚠️  /icons 页面内容较少 (${ICONS_SIZE} 字节)"
fi

echo ""
echo "6. 检查nginx代理配置..."

# 检查代理头部
PROXY_HEADERS=$(curl -s -I "$BASE_URL/icons" -k | grep -i "x-proxy-source\|cache-control\|expires")
if [ -n "$PROXY_HEADERS" ]; then
    echo "   ✅ nginx代理头部正确:"
    echo "$PROXY_HEADERS" | sed 's/^/     /'
else
    echo "   ❌ nginx代理头部缺失"
fi

echo ""
echo "7. 性能测试..."

# 测试响应时间
echo "   测试图标页面响应时间..."
RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" "$BASE_URL/icons" -k)
echo "   📊 /icons 响应时间: ${RESPONSE_TIME}s"

RESPONSE_TIME_CHAT=$(curl -s -o /dev/null -w "%{time_total}" "$BASE_URL/chat/icons" -k)
echo "   📊 /chat/icons 响应时间: ${RESPONSE_TIME_CHAT}s"

echo ""
echo "🎯 验证完成！"
echo "================================"

# 总结
if [ "$SUCCESS_COUNT" -gt 5 ]; then
    echo "✅ 图标系统工作正常！大部分图标文件都可以正确访问。"
else
    echo "⚠️  图标系统可能存在问题，建议检查LobeChat服务和nginx配置。"
fi

echo ""
echo "💡 提示："
echo "   - 如果某些图标文件返回404，这可能是正常的，因为LobeChat可能不包含所有标准图标"
echo "   - 重要的是 /icons 页面和基本图标文件（favicon.ico等）能正常访问"
echo "   - 如果页面显示正常但图标不显示，可能是浏览器缓存问题，尝试强制刷新"
