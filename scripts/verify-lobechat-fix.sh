#!/bin/bash

echo "🔍 验证 LobeChat 集成修复"
echo "================================"

# 测试1: LobeChat 服务可用性
echo "1. 测试 LobeChat 服务..."
if curl -s -I http://*************:3210 | grep -q "200\|302"; then
    echo "   ✅ LobeChat 服务运行正常"
else
    echo "   ❌ LobeChat 服务不可用"
fi

# 测试2: nginx 代理
echo "2. 测试 nginx 代理..."
if curl -s -I https://*************/chat/ -k | grep -q "200\|302"; then
    echo "   ✅ nginx 代理工作正常"
else
    echo "   ❌ nginx 代理有问题"
fi

# 测试3: 静态资源 - CSS
echo "3. 测试 CSS 静态资源..."
CSS_RESPONSE=$(curl -s -I "https://*************/_next/static/css/967104e9fd9cfb93.css" -k)
if echo "$CSS_RESPONSE" | grep -q "200" && echo "$CSS_RESPONSE" | grep -q "text/css"; then
    echo "   ✅ CSS 文件正确加载 (MIME: text/css)"
else
    echo "   ❌ CSS 文件加载失败"
fi

# 测试4: 静态资源 - JavaScript
echo "4. 测试 JavaScript 静态资源..."
JS_RESPONSE=$(curl -s -I "https://*************/_next/static/chunks/webpack-b35e42786ad727d1.js" -k)
if echo "$JS_RESPONSE" | grep -q "200" && echo "$JS_RESPONSE" | grep -q "application/javascript"; then
    echo "   ✅ JavaScript 文件正确加载 (MIME: application/javascript)"
else
    echo "   ❌ JavaScript 文件加载失败"
fi

# 测试5: 页面内容
echo "5. 测试页面内容..."
if curl -s "https://*************/chat/" -k | grep -q "LobeChat"; then
    echo "   ✅ LobeChat 页面内容正确"
else
    echo "   ❌ LobeChat 页面内容异常"
fi

# 测试6: 检查是否有 MIME 类型错误
echo "6. 检查 MIME 类型..."
CSS_CONTENT_TYPE=$(curl -s -I "https://*************/_next/static/css/967104e9fd9cfb93.css" -k | grep -i "content-type" | cut -d' ' -f2-)
JS_CONTENT_TYPE=$(curl -s -I "https://*************/_next/static/chunks/webpack-b35e42786ad727d1.js" -k | grep -i "content-type" | cut -d' ' -f2-)

echo "   CSS Content-Type: $CSS_CONTENT_TYPE"
echo "   JS Content-Type: $JS_CONTENT_TYPE"

if [[ "$CSS_CONTENT_TYPE" == *"text/css"* ]] && [[ "$JS_CONTENT_TYPE" == *"application/javascript"* ]]; then
    echo "   ✅ MIME 类型正确"
else
    echo "   ❌ MIME 类型不正确"
fi

echo ""
echo "🎯 修复验证完成！"
echo ""
echo "📋 下一步测试建议："
echo "1. 在浏览器中访问: https://*************/test-lobechat.html"
echo "2. 在浏览器中访问: https://*************/#chat"
echo "3. 检查浏览器控制台是否还有 MIME 类型错误"
echo "4. 验证 LobeChat 是否可以正常加载和交互"
