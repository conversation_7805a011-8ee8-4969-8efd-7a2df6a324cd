#!/bin/bash

echo "🔍 LobeChat 静态资源全面测试"
echo "================================"

# 测试所有可能的静态资源类型
STATIC_RESOURCES=(
    # Next.js 静态资源
    "/_next/static/chunks/webpack-b35e42786ad727d1.js"
    "/_next/static/css/967104e9fd9cfb93.css"
    
    # Favicon 文件
    "/favicon.ico"
    "/favicon-16x16.ico"
    "/favicon-32x32.ico"
    "/favicon-96x96.ico"
    "/favicon.png"
    "/favicon.svg"
    
    # Apple 图标
    "/apple-touch-icon.png"
    "/apple-touch-icon-57x57.png"
    "/apple-touch-icon-60x60.png"
    "/apple-touch-icon-72x72.png"
    "/apple-touch-icon-76x76.png"
    "/apple-touch-icon-114x114.png"
    "/apple-touch-icon-120x120.png"
    "/apple-touch-icon-144x144.png"
    "/apple-touch-icon-152x152.png"
    "/apple-touch-icon-180x180.png"
    
    # Manifest 文件
    "/manifest.json"
    "/manifest.webmanifest"
    "/site.webmanifest"
    
    # SEO 文件
    "/robots.txt"
    "/sitemap.xml"
)

# 测试带版本参数的资源
VERSIONED_RESOURCES=(
    "/favicon-32x32.ico?v=1"
    "/apple-touch-icon.png?v=1"
    "/favicon.ico?v=1"
)

echo "1. 测试基础静态资源..."
SUCCESS_COUNT=0
TOTAL_COUNT=0

for resource in "${STATIC_RESOURCES[@]}"; do
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    echo -n "   测试 $resource ... "
    
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://192.168.60.52$resource" -k)
    PROXY_SOURCE=$(curl -s -I "https://192.168.60.52$resource" -k | grep -i "x-proxy-source" | cut -d' ' -f2- | tr -d '\r')
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ (HTTP $HTTP_CODE, Proxy: $PROXY_SOURCE)"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "❌ (HTTP $HTTP_CODE)"
    fi
done

echo ""
echo "2. 测试带版本参数的资源..."

for resource in "${VERSIONED_RESOURCES[@]}"; do
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    echo -n "   测试 $resource ... "
    
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://192.168.60.52$resource" -k)
    PROXY_SOURCE=$(curl -s -I "https://192.168.60.52$resource" -k | grep -i "x-proxy-source" | cut -d' ' -f2- | tr -d '\r')
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ (HTTP $HTTP_CODE, Proxy: $PROXY_SOURCE)"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "❌ (HTTP $HTTP_CODE)"
    fi
done

echo ""
echo "3. 测试路径重写后的资源..."

# 测试重写后的路径
REWRITTEN_RESOURCES=(
    "/chat/_next/static/chunks/webpack-b35e42786ad727d1.js"
    "/chat/_next/static/css/967104e9fd9cfb93.css"
    "/chat/favicon-32x32.ico?v=1"
    "/chat/apple-touch-icon.png?v=1"
    "/chat/manifest.webmanifest"
)

for resource in "${REWRITTEN_RESOURCES[@]}"; do
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    echo -n "   测试 $resource ... "
    
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://192.168.60.52$resource" -k)
    PROXY_SOURCE=$(curl -s -I "https://192.168.60.52$resource" -k | grep -i "x-proxy-source" | cut -d' ' -f2- | tr -d '\r')
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ (HTTP $HTTP_CODE, Proxy: $PROXY_SOURCE)"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "❌ (HTTP $HTTP_CODE)"
    fi
done

echo ""
echo "4. 验证 MIME 类型..."

# 检查关键文件的 MIME 类型
echo "   CSS MIME 类型:"
CSS_MIME=$(curl -s -I "https://192.168.60.52/_next/static/css/967104e9fd9cfb93.css" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $CSS_MIME"

echo "   JS MIME 类型:"
JS_MIME=$(curl -s -I "https://192.168.60.52/_next/static/chunks/webpack-b35e42786ad727d1.js" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $JS_MIME"

echo "   PNG MIME 类型:"
PNG_MIME=$(curl -s -I "https://192.168.60.52/apple-touch-icon.png?v=1" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $PNG_MIME"

echo "   ICO MIME 类型:"
ICO_MIME=$(curl -s -I "https://192.168.60.52/favicon-32x32.ico?v=1" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $ICO_MIME"

echo ""
echo "5. 检查路径重写效果..."

# 检查HTML中的路径重写
UNREWRITTEN=$(curl -s "https://192.168.60.52/chat/" -k | grep -oE '(src|href)="/_next/static/[^"]*"' | wc -l)
REWRITTEN=$(curl -s "https://192.168.60.52/chat/" -k | grep -oE '(src|href)="/chat/_next/static/[^"]*"' | wc -l)
FAVICON_REWRITTEN=$(curl -s "https://192.168.60.52/chat/" -k | grep -c '/chat/favicon' || echo "0")
APPLE_ICON_REWRITTEN=$(curl -s "https://192.168.60.52/chat/" -k | grep -c '/chat/apple-touch-icon' || echo "0")

echo "   未重写的 _next/static 路径: $UNREWRITTEN"
echo "   已重写的 _next/static 路径: $REWRITTEN"
echo "   重写的 favicon 路径: $FAVICON_REWRITTEN"
echo "   重写的 apple-touch-icon 路径: $APPLE_ICON_REWRITTEN"

echo ""
echo "📊 测试结果统计:"
echo "================================"
echo "   总测试资源数: $TOTAL_COUNT"
echo "   成功访问数: $SUCCESS_COUNT"
echo "   失败数: $((TOTAL_COUNT - SUCCESS_COUNT))"
echo "   成功率: $(( SUCCESS_COUNT * 100 / TOTAL_COUNT ))%"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ] && [ "$UNREWRITTEN" -eq 0 ]; then
    echo ""
    echo "🎉 完美！所有静态资源测试通过："
    echo "   ✅ 所有资源都可以正常访问"
    echo "   ✅ 路径重写完全成功"
    echo "   ✅ MIME 类型正确设置"
    echo "   ✅ 代理路由工作正常"
    echo ""
    echo "🌐 LobeChat 现在应该可以完全正常加载了！"
    echo "   测试地址: https://192.168.60.52/#chat"
else
    echo ""
    echo "⚠️  仍有问题需要解决："
    if [ $SUCCESS_COUNT -ne $TOTAL_COUNT ]; then
        echo "   - 有 $((TOTAL_COUNT - SUCCESS_COUNT)) 个资源无法访问"
    fi
    if [ "$UNREWRITTEN" -ne 0 ]; then
        echo "   - 有 $UNREWRITTEN 个路径未被重写"
    fi
fi
