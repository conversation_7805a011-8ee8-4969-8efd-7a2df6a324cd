#!/bin/bash

echo "🔌 LobeChat API 代理测试"
echo "================================"

BASE_URL="https://192.168.60.52"

# API端点列表
API_ENDPOINTS=(
    "/webapi/chat/openai"
    "/webapi/chat/anthropic"
    "/webapi/chat/google"
    "/api/auth/session"
    "/api/auth/providers"
    "/api/config"
    "/chat/webapi/chat/openai"
    "/chat/api/auth/session"
    "/chat/api/config"
)

echo ""
echo "1. 测试API端点可达性..."

for endpoint in "${API_ENDPOINTS[@]}"; do
    echo "   测试: $endpoint"
    
    # 测试GET请求
    GET_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$endpoint" -k)
    echo "     GET: HTTP $GET_STATUS"
    
    # 测试POST请求
    POST_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$endpoint" -k -X POST)
    echo "     POST: HTTP $POST_STATUS"
    
    # 检查代理源头
    PROXY_SOURCE=$(curl -s -I "$BASE_URL$endpoint" -k | grep -i "x-proxy-source" | cut -d: -f2 | tr -d ' \r')
    if [ -n "$PROXY_SOURCE" ]; then
        echo "     代理源: $PROXY_SOURCE"
    else
        echo "     代理源: 未找到"
    fi
    
    echo ""
done

echo "2. 测试CORS支持..."

# 测试OPTIONS请求
echo "   测试 /webapi/chat/openai 的CORS..."
OPTIONS_RESPONSE=$(curl -s -I "$BASE_URL/webapi/chat/openai" -k -X OPTIONS)
if echo "$OPTIONS_RESPONSE" | grep -q "Access-Control-Allow"; then
    echo "     ✅ CORS头部存在"
else
    echo "     ⚠️  CORS头部缺失"
fi

echo ""
echo "3. 测试WebSocket升级支持..."

# 测试WebSocket升级头部
WS_RESPONSE=$(curl -s -I "$BASE_URL/webapi/chat/openai" -k \
    -H "Connection: Upgrade" \
    -H "Upgrade: websocket" \
    -H "Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==" \
    -H "Sec-WebSocket-Version: 13")

if echo "$WS_RESPONSE" | grep -q "101\|426"; then
    echo "   ✅ WebSocket升级支持正常"
else
    echo "   ⚠️  WebSocket升级可能有问题"
fi

echo ""
echo "4. 测试认证头部传递..."

# 测试认证头部是否正确传递
AUTH_RESPONSE=$(curl -s -I "$BASE_URL/webapi/chat/openai" -k \
    -H "Authorization: Bearer test-token" \
    -H "X-Auth-User: testuser" \
    -H "X-Auth-Email: <EMAIL>")

echo "   认证测试响应状态: $(echo "$AUTH_RESPONSE" | head -1)"

echo ""
echo "5. 测试流式响应支持..."

# 测试流式响应（应该不会被缓冲）
echo "   测试流式响应配置..."
STREAM_HEADERS=$(curl -s -I "$BASE_URL/webapi/chat/openai" -k -X POST | grep -i "transfer-encoding\|content-length")
if [ -n "$STREAM_HEADERS" ]; then
    echo "     响应头部:"
    echo "$STREAM_HEADERS" | sed 's/^/       /'
else
    echo "     ⚠️  未找到相关响应头部"
fi

echo ""
echo "6. 检查nginx日志..."

# 检查webapi日志
if docker exec ynnx-nginx test -f /var/log/nginx/lobechat_webapi.log 2>/dev/null; then
    echo "   ✅ webapi日志文件存在"
    WEBAPI_LOG_COUNT=$(docker exec ynnx-nginx wc -l /var/log/nginx/lobechat_webapi.log 2>/dev/null | cut -d' ' -f1)
    echo "   📊 webapi访问记录: $WEBAPI_LOG_COUNT 条"
    
    # 显示最近的几条日志
    echo "   最近的webapi访问:"
    docker exec ynnx-nginx tail -3 /var/log/nginx/lobechat_webapi.log 2>/dev/null | sed 's/^/     /' || echo "     无法读取日志内容"
else
    echo "   ❌ webapi日志文件不存在"
fi

echo ""
echo "7. 测试错误处理..."

# 测试不存在的API端点
ERROR_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/webapi/nonexistent" -k)
echo "   不存在的API端点: HTTP $ERROR_STATUS"

# 测试超大请求
echo "   测试大请求处理..."
LARGE_DATA=$(printf 'a%.0s' {1..1000})
LARGE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/webapi/chat/openai" -k -X POST -d "$LARGE_DATA")
echo "   大请求状态: HTTP $LARGE_STATUS"

echo ""
echo "🎯 测试完成！"
echo "================================"

# 总结
echo "💡 重要提示："
echo "   - 401/403错误是正常的，说明API端点存在但需要认证"
echo "   - 404错误表示端点不存在或代理配置有问题"
echo "   - 405错误表示HTTP方法不被允许"
echo "   - 500错误表示服务器内部错误"
echo ""
echo "   如果看到405错误，说明nginx代理配置已经修复！"
echo "   现在LobeChat应该能够正常发送聊天消息了。"
