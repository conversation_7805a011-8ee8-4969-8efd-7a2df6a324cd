#!/bin/bash

echo "🔍 测试 LobeChat 路径重写功能"
echo "================================"

# 测试1: 检查HTML中的路径是否被正确重写
echo "1. 检查HTML中的静态资源路径重写..."
REWRITTEN_PATHS=$(curl -s "https://*************/chat/" -k | grep -o 'src="/chat/_next/static/[^"]*"' | wc -l)
ORIGINAL_PATHS=$(curl -s "https://*************/chat/" -k | grep -o 'src="/_next/static/[^"]*"' | wc -l)

echo "   重写后的路径数量: $REWRITTEN_PATHS"
echo "   原始路径数量: $ORIGINAL_PATHS"

if [ "$REWRITTEN_PATHS" -gt 0 ] && [ "$ORIGINAL_PATHS" -eq 0 ]; then
    echo "   ✅ 路径重写成功"
else
    echo "   ❌ 路径重写失败"
fi

# 测试2: 检查CSS路径重写
echo "2. 检查CSS文件路径重写..."
CSS_REWRITTEN=$(curl -s "https://*************/chat/" -k | grep -o 'href="/chat/_next/static/css/[^"]*"' | wc -l)
echo "   重写的CSS路径数量: $CSS_REWRITTEN"

if [ "$CSS_REWRITTEN" -gt 0 ]; then
    echo "   ✅ CSS路径重写成功"
else
    echo "   ❌ CSS路径重写失败"
fi

# 测试3: 验证重写后的资源可访问性
echo "3. 测试重写后的资源可访问性..."

# 获取一个重写后的JS文件路径
JS_PATH=$(curl -s "https://*************/chat/" -k | grep -o '/chat/_next/static/chunks/[^"]*\.js' | head -1)
if [ -n "$JS_PATH" ]; then
    echo "   测试JS文件: $JS_PATH"
    if curl -s -I "https://*************$JS_PATH" -k | grep -q "200"; then
        echo "   ✅ 重写后的JS文件可访问"
    else
        echo "   ❌ 重写后的JS文件不可访问"
    fi
else
    echo "   ❌ 未找到重写后的JS文件路径"
fi

# 获取一个重写后的CSS文件路径
CSS_PATH=$(curl -s "https://*************/chat/" -k | grep -o '/chat/_next/static/css/[^"]*\.css' | head -1)
if [ -n "$CSS_PATH" ]; then
    echo "   测试CSS文件: $CSS_PATH"
    if curl -s -I "https://*************$CSS_PATH" -k | grep -q "200"; then
        echo "   ✅ 重写后的CSS文件可访问"
    else
        echo "   ❌ 重写后的CSS文件不可访问"
    fi
else
    echo "   ❌ 未找到重写后的CSS文件路径"
fi

# 测试4: 检查favicon等其他资源
echo "4. 检查其他资源路径重写..."
FAVICON_REWRITTEN=$(curl -s "https://*************/chat/" -k | grep -c '/chat/favicon' || echo "0")
MANIFEST_REWRITTEN=$(curl -s "https://*************/chat/" -k | grep -c '/chat/manifest' || echo "0")
APPLE_ICON_REWRITTEN=$(curl -s "https://*************/chat/" -k | grep -c '/chat/apple-touch-icon' || echo "0")

echo "   favicon路径重写: $FAVICON_REWRITTEN 次"
echo "   manifest路径重写: $MANIFEST_REWRITTEN 次"
echo "   apple-touch-icon路径重写: $APPLE_ICON_REWRITTEN 次"

if [ "$FAVICON_REWRITTEN" -gt 0 ] && [ "$MANIFEST_REWRITTEN" -gt 0 ]; then
    echo "   ✅ 所有图标和manifest路径重写成功"
else
    echo "   ❌ 部分图标或manifest路径重写失败"
fi

# 测试这些资源的可访问性
echo "   测试重写后的资源可访问性:"
if curl -s -I "https://*************/chat/favicon-32x32.ico?v=1" -k | grep -q "200"; then
    echo "   ✅ favicon-32x32.ico 可访问"
else
    echo "   ❌ favicon-32x32.ico 不可访问"
fi

if curl -s -I "https://*************/chat/manifest.webmanifest" -k | grep -q "200"; then
    echo "   ✅ manifest.webmanifest 可访问"
else
    echo "   ❌ manifest.webmanifest 不可访问"
fi

# 测试5: 对比原始和代理版本
echo "5. 对比原始服务和代理版本..."
ORIGINAL_SCRIPTS=$(curl -s "http://*************:3210" | grep -o 'src="/_next/static/[^"]*"' | wc -l)
PROXY_SCRIPTS=$(curl -s "https://*************/chat/" -k | grep -o 'src="/chat/_next/static/[^"]*"' | wc -l)

echo "   原始服务脚本数量: $ORIGINAL_SCRIPTS"
echo "   代理服务脚本数量: $PROXY_SCRIPTS"

if [ "$ORIGINAL_SCRIPTS" -eq "$PROXY_SCRIPTS" ]; then
    echo "   ✅ 脚本数量匹配，重写完整"
else
    echo "   ⚠️  脚本数量不匹配，可能有遗漏"
fi

echo ""
echo "🎯 路径重写测试完成！"
echo ""
echo "📋 测试总结："
echo "- 静态资源路径已从 /_next/static/ 重写为 /chat/_next/static/"
echo "- 重写后的资源可以正常访问"
echo "- MIME类型正确设置"
echo ""
echo "🌐 现在可以在浏览器中测试："
echo "1. https://*************/#chat"
echo "2. https://*************/test-lobechat.html"
