#!/bin/bash

echo "🎯 LobeChat 集成最终验证"
echo "================================"

# 关键资源列表 - 这些是LobeChat正常运行必需的
CRITICAL_RESOURCES=(
    # Next.js 核心资源
    "/_next/static/chunks/webpack-b35e42786ad727d1.js"
    "/_next/static/css/967104e9fd9cfb93.css"
    "/_next/static/chunks/main-app-3fee04e2adb5c413.js"
    
    # 重要图标
    "/favicon-32x32.ico?v=1"
    "/apple-touch-icon.png?v=1"
    "/manifest.webmanifest"
    
    # 重写后的路径
    "/chat/_next/static/chunks/webpack-b35e42786ad727d1.js"
    "/chat/_next/static/css/967104e9fd9cfb93.css"
    "/chat/favicon-32x32.ico?v=1"
    "/chat/apple-touch-icon.png?v=1"
)

echo "1. 测试关键静态资源..."
CRITICAL_SUCCESS=0
CRITICAL_TOTAL=${#CRITICAL_RESOURCES[@]}

for resource in "${CRITICAL_RESOURCES[@]}"; do
    echo -n "   $resource ... "
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://*************$resource" -k)
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅"
        CRITICAL_SUCCESS=$((CRITICAL_SUCCESS + 1))
    else
        echo "❌ (HTTP $HTTP_CODE)"
    fi
done

echo ""
echo "2. 验证LobeChat页面加载..."

# 检查LobeChat页面是否正确加载
CHAT_PAGE=$(curl -s "https://*************/chat/" -k)
if echo "$CHAT_PAGE" | grep -q "LobeChat"; then
    echo "   ✅ LobeChat页面内容正确"
    PAGE_LOAD_OK=true
else
    echo "   ❌ LobeChat页面内容异常"
    PAGE_LOAD_OK=false
fi

# 检查路径重写是否正确
REWRITTEN_COUNT=$(echo "$CHAT_PAGE" | grep -oE '(src|href)="/chat/_next/static/[^"]*"' | wc -l)
UNREWRITTEN_COUNT=$(echo "$CHAT_PAGE" | grep -oE '(src|href)="/_next/static/[^"]*"' | wc -l)

echo "   重写的静态资源路径: $REWRITTEN_COUNT"
echo "   未重写的静态资源路径: $UNREWRITTEN_COUNT"

if [ "$REWRITTEN_COUNT" -gt 0 ] && [ "$UNREWRITTEN_COUNT" -eq 0 ]; then
    echo "   ✅ 路径重写完全成功"
    PATH_REWRITE_OK=true
else
    echo "   ❌ 路径重写不完整"
    PATH_REWRITE_OK=false
fi

echo ""
echo "3. 检查MIME类型设置..."

# 验证关键文件的MIME类型
CSS_MIME=$(curl -s -I "https://*************/_next/static/css/967104e9fd9cfb93.css" -k | grep -i "content-type" | grep -q "text/css")
JS_MIME=$(curl -s -I "https://*************/_next/static/chunks/webpack-b35e42786ad727d1.js" -k | grep -i "content-type" | grep -q "application/javascript")
PNG_MIME=$(curl -s -I "https://*************/apple-touch-icon.png?v=1" -k | grep -i "content-type" | grep -q "image/png")

if $CSS_MIME && $JS_MIME && $PNG_MIME; then
    echo "   ✅ 所有MIME类型正确设置"
    MIME_OK=true
else
    echo "   ❌ 部分MIME类型设置有问题"
    MIME_OK=false
fi

echo ""
echo "4. 检查代理路由..."

# 验证代理路由是否正确工作
STATIC_PROXY=$(curl -s -I "https://*************/apple-touch-icon.png?v=1" -k | grep -i "x-proxy-source" | grep -q "lobechat-static")
PREFIXED_PROXY=$(curl -s -I "https://*************/chat/apple-touch-icon.png?v=1" -k | grep -i "x-proxy-source" | grep -q "lobechat-prefixed")

if $STATIC_PROXY && $PREFIXED_PROXY; then
    echo "   ✅ 代理路由工作正常"
    PROXY_OK=true
else
    echo "   ❌ 代理路由有问题"
    PROXY_OK=false
fi

echo ""
echo "📊 最终验证结果:"
echo "================================"
echo "   关键资源访问: $CRITICAL_SUCCESS/$CRITICAL_TOTAL ($(( CRITICAL_SUCCESS * 100 / CRITICAL_TOTAL ))%)"
echo "   页面加载: $([ "$PAGE_LOAD_OK" = true ] && echo "✅ 正常" || echo "❌ 异常")"
echo "   路径重写: $([ "$PATH_REWRITE_OK" = true ] && echo "✅ 正常" || echo "❌ 异常")"
echo "   MIME类型: $([ "$MIME_OK" = true ] && echo "✅ 正常" || echo "❌ 异常")"
echo "   代理路由: $([ "$PROXY_OK" = true ] && echo "✅ 正常" || echo "❌ 异常")"

# 综合评估
if [ $CRITICAL_SUCCESS -eq $CRITICAL_TOTAL ] && [ "$PAGE_LOAD_OK" = true ] && [ "$PATH_REWRITE_OK" = true ] && [ "$MIME_OK" = true ] && [ "$PROXY_OK" = true ]; then
    echo ""
    echo "🎉 恭喜！LobeChat集成完全成功！"
    echo ""
    echo "✅ 所有关键功能都正常工作："
    echo "   • 静态资源完全可访问"
    echo "   • 路径重写机制完善"
    echo "   • MIME类型正确设置"
    echo "   • 代理路由智能分发"
    echo "   • 页面内容正确加载"
    echo ""
    echo "🌐 现在可以正常使用LobeChat了："
    echo "   主平台: https://*************/#chat"
    echo "   直接访问: https://*************/chat/"
    echo ""
    echo "🔧 技术特性："
    echo "   • 通用静态资源代理 - 自动捕获所有可能的资源请求"
    echo "   • 智能路径重写 - 支持所有图标和manifest文件"
    echo "   • 双层代理机制 - 同时支持直接访问和重写路径"
    echo "   • 完善的MIME类型处理 - 确保浏览器正确识别文件类型"
    echo "   • 调试友好 - 包含代理来源标识头部"
    
    exit 0
else
    echo ""
    echo "⚠️  集成仍有问题需要解决："
    
    if [ $CRITICAL_SUCCESS -ne $CRITICAL_TOTAL ]; then
        echo "   • 有 $(( CRITICAL_TOTAL - CRITICAL_SUCCESS )) 个关键资源无法访问"
    fi
    
    if [ "$PAGE_LOAD_OK" != true ]; then
        echo "   • LobeChat页面加载异常"
    fi
    
    if [ "$PATH_REWRITE_OK" != true ]; then
        echo "   • 路径重写机制不完整"
    fi
    
    if [ "$MIME_OK" != true ]; then
        echo "   • MIME类型设置有问题"
    fi
    
    if [ "$PROXY_OK" != true ]; then
        echo "   • 代理路由配置有问题"
    fi
    
    echo ""
    echo "🔧 建议检查："
    echo "   1. nginx配置是否正确重新加载"
    echo "   2. LobeChat服务是否正常运行"
    echo "   3. 网络连接是否正常"
    echo "   4. 浏览器缓存是否需要清理"
    
    exit 1
fi
