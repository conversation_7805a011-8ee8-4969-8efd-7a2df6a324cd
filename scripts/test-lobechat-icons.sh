#!/bin/bash

echo "🎨 LobeChat 图标路径测试"
echo "================================"

# 测试配置
BASE_URL="https://192.168.60.52"
LOBECHAT_URL="http://192.168.60.52:3210"

# 测试路径列表
ICON_PATHS=(
    "/icons"
    "/chat/icons"
)

# 常见图标文件路径
ICON_FILES=(
    "/favicon.ico"
    "/favicon-32x32.ico"
    "/apple-touch-icon.png"
    "/manifest.webmanifest"
    "/chat/favicon.ico"
    "/chat/favicon-32x32.ico"
    "/chat/apple-touch-icon.png"
    "/chat/manifest.webmanifest"
)

echo ""
echo "1. 测试图标页面访问..."
for path in "${ICON_PATHS[@]}"; do
    echo "   测试路径: $path"
    
    # 测试HTTP状态码
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$path" -k)
    if [ "$STATUS" = "200" ]; then
        echo "     ✅ HTTP状态: $STATUS"
    else
        echo "     ❌ HTTP状态: $STATUS"
    fi
    
    # 检查代理源头
    PROXY_SOURCE=$(curl -s -I "$BASE_URL$path" -k | grep -i "x-proxy-source" | cut -d: -f2 | tr -d ' \r')
    if [ -n "$PROXY_SOURCE" ]; then
        echo "     ✅ 代理源: $PROXY_SOURCE"
    else
        echo "     ❌ 代理源: 未找到"
    fi
    
    echo ""
done

echo "2. 测试图标文件访问..."
for file in "${ICON_FILES[@]}"; do
    echo "   测试文件: $file"
    
    # 测试HTTP状态码
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$file" -k)
    if [ "$STATUS" = "200" ]; then
        echo "     ✅ HTTP状态: $STATUS"
    else
        echo "     ❌ HTTP状态: $STATUS"
    fi
    
    # 检查Content-Type
    CONTENT_TYPE=$(curl -s -I "$BASE_URL$file" -k | grep -i "content-type" | cut -d: -f2 | tr -d ' \r')
    if [ -n "$CONTENT_TYPE" ]; then
        echo "     ✅ Content-Type: $CONTENT_TYPE"
    else
        echo "     ❌ Content-Type: 未找到"
    fi
    
    echo ""
done

echo "3. 测试图标页面内容..."
echo "   检查 /icons 页面是否包含图标相关内容..."

# 获取页面内容并检查是否包含图标相关的元素
ICONS_CONTENT=$(curl -s "$BASE_URL/icons" -k)
if echo "$ICONS_CONTENT" | grep -q "icon\|svg\|png"; then
    echo "     ✅ 页面包含图标相关内容"
else
    echo "     ❌ 页面不包含图标相关内容"
fi

# 检查页面是否正确加载了CSS和JS
if echo "$ICONS_CONTENT" | grep -q "_next/static"; then
    echo "     ✅ 页面正确加载了静态资源"
else
    echo "     ❌ 页面未正确加载静态资源"
fi

echo ""
echo "4. 测试直接访问LobeChat服务..."
echo "   测试 LobeChat 服务的 /icons 路径..."

DIRECT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$LOBECHAT_URL/icons")
if [ "$DIRECT_STATUS" = "200" ]; then
    echo "     ✅ LobeChat服务 /icons 路径正常: $DIRECT_STATUS"
else
    echo "     ❌ LobeChat服务 /icons 路径异常: $DIRECT_STATUS"
fi

echo ""
echo "5. 检查nginx日志..."
echo "   检查图标相关的访问日志..."

# 检查nginx容器中的日志文件
if docker exec ynnx-nginx test -f /var/log/nginx/lobechat_icons.log 2>/dev/null; then
    echo "     ✅ 图标日志文件存在"
    ICON_LOG_COUNT=$(docker exec ynnx-nginx wc -l /var/log/nginx/lobechat_icons.log 2>/dev/null | cut -d' ' -f1)
    echo "     📊 图标访问记录: $ICON_LOG_COUNT 条"
else
    echo "     ❌ 图标日志文件不存在"
fi

if docker exec ynnx-nginx test -f /var/log/nginx/lobechat_prefixed_icons.log 2>/dev/null; then
    echo "     ✅ 带前缀图标日志文件存在"
    PREFIXED_LOG_COUNT=$(docker exec ynnx-nginx wc -l /var/log/nginx/lobechat_prefixed_icons.log 2>/dev/null | cut -d' ' -f1)
    echo "     📊 带前缀图标访问记录: $PREFIXED_LOG_COUNT 条"
else
    echo "     ❌ 带前缀图标日志文件不存在"
fi

echo ""
echo "🎯 测试完成！"
echo "================================"
echo "如果所有测试都通过，说明LobeChat的图标路径代理已经正确配置。"
echo "如果有测试失败，请检查nginx配置和LobeChat服务状态。"
