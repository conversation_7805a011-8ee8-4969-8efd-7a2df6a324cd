#!/usr/bin/env node

/**
 * LobeChat 集成测试脚本
 * 用于验证 LobeChat 与 YNNX 平台的集成是否正常工作
 */

import https from 'https';
import http from 'http';

// 测试配置
const TEST_CONFIG = {
  platform: {
    host: '*************',
    port: 443,
    protocol: 'https'
  },
  lobechat: {
    host: '*************',
    port: 3210,
    protocol: 'http'
  },
  nginx: {
    host: '*************',
    port: 443,
    protocol: 'https',
    chatPath: '/chat/'
  }
};

// 测试用户数据
const TEST_USERS = [
  {
    username: 'testuser1',
    name: 'Test User 1',
    email: '<EMAIL>'
  },
  {
    username: 'testuser2',
    name: 'Test User 2',
    email: '<EMAIL>'
  }
];

/**
 * 发送 HTTP 请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    // 处理协议选择
    const isHttps = options.protocol === 'https:' || options.port === 443;
    const client = isHttps ? https : http;

    // 为 HTTPS 请求添加忽略证书验证（仅用于测试）
    if (isHttps) {
      options.rejectUnauthorized = false;
    }

    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(data);
    }
    req.end();
  });
}

/**
 * 测试 LobeChat 服务可用性
 */
async function testLobeChatService() {
  console.log('🔍 测试 LobeChat 服务可用性...');
  
  try {
    const options = {
      hostname: TEST_CONFIG.lobechat.host,
      port: TEST_CONFIG.lobechat.port,
      path: '/',
      method: 'GET',
      protocol: TEST_CONFIG.lobechat.protocol + ':',
      timeout: 5000
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 200 || response.statusCode === 302) {
      console.log('✅ LobeChat 服务运行正常');
      return true;
    } else {
      console.log(`❌ LobeChat 服务响应异常: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ LobeChat 服务连接失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试 nginx 代理配置
 */
async function testNginxProxy() {
  console.log('🔍 测试 nginx 代理配置...');
  
  try {
    const options = {
      hostname: TEST_CONFIG.nginx.host,
      port: TEST_CONFIG.nginx.port,
      path: TEST_CONFIG.nginx.chatPath,
      method: 'GET',
      protocol: TEST_CONFIG.nginx.protocol + ':',
      headers: {
        'User-Agent': 'YNNX-Integration-Test',
        'X-Auth-User': 'testuser',
        'X-Auth-Email': '<EMAIL>',
        'X-Auth-Name': '测试用户',
        'X-Platform-Auth': 'ynnx-platform',
        'X-Platform-Token': 'test-token'
      },
      timeout: 10000,
      // 忽略 SSL 证书错误（仅用于测试）
      rejectUnauthorized: false
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 200 || response.statusCode === 302) {
      console.log('✅ nginx 代理配置正常');
      return true;
    } else {
      console.log(`❌ nginx 代理响应异常: ${response.statusCode}`);
      console.log('响应头:', response.headers);
      return false;
    }
  } catch (error) {
    console.log(`❌ nginx 代理连接失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试认证头部传递
 */
async function testAuthHeaders() {
  console.log('🔍 测试认证头部传递...');
  
  const testHeaders = {
    'X-Auth-User': 'testuser123',
    'X-Auth-Email': '<EMAIL>',
    'X-Auth-Name': 'Test User 123',
    'X-Platform-Auth': 'ynnx-platform',
    'X-Platform-Token': 'test-token-123'
  };

  try {
    const options = {
      hostname: TEST_CONFIG.nginx.host,
      port: TEST_CONFIG.nginx.port,
      path: TEST_CONFIG.nginx.chatPath + 'api/auth/session',
      method: 'GET',
      protocol: TEST_CONFIG.nginx.protocol + ':',
      headers: {
        ...testHeaders,
        'User-Agent': 'YNNX-Auth-Test'
      },
      timeout: 10000,
      rejectUnauthorized: false
    };

    const response = await makeRequest(options);
    
    console.log(`认证端点响应状态: ${response.statusCode}`);
    
    // 检查是否正确传递了认证信息
    if (response.statusCode < 500) {
      console.log('✅ 认证头部传递正常');
      return true;
    } else {
      console.log('❌ 认证头部传递可能有问题');
      return false;
    }
  } catch (error) {
    console.log(`❌ 认证头部测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试用户会话隔离
 */
async function testUserSessionIsolation() {
  console.log('🔍 测试用户会话隔离...');
  
  const results = [];
  
  for (const user of TEST_USERS) {
    try {
      const options = {
        hostname: TEST_CONFIG.nginx.host,
        port: TEST_CONFIG.nginx.port,
        path: TEST_CONFIG.nginx.chatPath,
        method: 'GET',
        protocol: TEST_CONFIG.nginx.protocol + ':',
        headers: {
          'X-Auth-User': user.username,
          'X-Auth-Email': user.email,
          'X-Auth-Name': user.name,
          'X-Platform-Auth': 'ynnx-platform',
          'X-Platform-Token': `token-${user.username}`,
          'User-Agent': `YNNX-User-${user.username}`
        },
        timeout: 10000,
        rejectUnauthorized: false
      };

      const response = await makeRequest(options);
      results.push({
        user: user.username,
        statusCode: response.statusCode,
        success: response.statusCode < 400
      });
      
      console.log(`用户 ${user.username}: ${response.statusCode}`);
      
    } catch (error) {
      console.log(`用户 ${user.username} 测试失败: ${error.message}`);
      results.push({
        user: user.username,
        success: false,
        error: error.message
      });
    }
  }
  
  const successCount = results.filter(r => r.success).length;
  if (successCount === TEST_USERS.length) {
    console.log('✅ 用户会话隔离测试通过');
    return true;
  } else {
    console.log(`❌ 用户会话隔离测试部分失败 (${successCount}/${TEST_USERS.length})`);
    return false;
  }
}

/**
 * 测试 WebSocket 连接
 */
async function testWebSocketSupport() {
  console.log('🔍 测试 WebSocket 支持...');
  
  try {
    const options = {
      hostname: TEST_CONFIG.nginx.host,
      port: TEST_CONFIG.nginx.port,
      path: TEST_CONFIG.nginx.chatPath,
      method: 'GET',
      protocol: TEST_CONFIG.nginx.protocol + ':',
      headers: {
        'Connection': 'Upgrade',
        'Upgrade': 'websocket',
        'Sec-WebSocket-Key': 'dGhlIHNhbXBsZSBub25jZQ==',
        'Sec-WebSocket-Version': '13',
        'X-Auth-User': 'wstest',
        'X-Platform-Auth': 'ynnx-platform'
      },
      timeout: 5000,
      rejectUnauthorized: false
    };

    const response = await makeRequest(options);
    
    // WebSocket 升级请求应该返回 101 或被正确代理
    if (response.statusCode === 101 || response.statusCode === 200) {
      console.log('✅ WebSocket 支持正常');
      return true;
    } else {
      console.log(`⚠️  WebSocket 支持可能有问题: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`⚠️  WebSocket 测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始 LobeChat 集成测试\n');
  
  const tests = [
    { name: 'LobeChat 服务可用性', fn: testLobeChatService },
    { name: 'nginx 代理配置', fn: testNginxProxy },
    { name: '认证头部传递', fn: testAuthHeaders },
    { name: '用户会话隔离', fn: testUserSessionIsolation },
    { name: 'WebSocket 支持', fn: testWebSocketSupport }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n--- ${test.name} ---`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      console.log(`❌ ${test.name} 测试异常: ${error.message}`);
      results.push({ name: test.name, success: false, error: error.message });
    }
  }
  
  // 输出测试总结
  console.log('\n📊 测试结果总结:');
  console.log('='.repeat(50));
  
  let passCount = 0;
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    console.log(`${result.name}: ${status}`);
    if (result.success) passCount++;
  });
  
  console.log('='.repeat(50));
  console.log(`总计: ${passCount}/${results.length} 项测试通过`);
  
  if (passCount === results.length) {
    console.log('\n🎉 所有测试通过！LobeChat 集成配置正确。');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查配置和服务状态。');
    console.log('\n📖 故障排除建议:');
    console.log('1. 确认 LobeChat 服务运行在 http://*************:3210');
    console.log('2. 检查 nginx 配置文件中的代理设置');
    console.log('3. 验证防火墙和网络连接');
    console.log('4. 查看服务日志获取详细错误信息');
    process.exit(1);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

export {
  runAllTests,
  testLobeChatService,
  testNginxProxy,
  testAuthHeaders,
  testUserSessionIsolation,
  testWebSocketSupport
};
