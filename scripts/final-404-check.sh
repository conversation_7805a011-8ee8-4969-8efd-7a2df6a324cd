#!/bin/bash

echo "🔍 最终404错误检查"
echo "================================"

# 从LobeChat页面提取所有资源链接并测试
echo "1. 提取并测试所有静态资源..."

# 提取所有src和href链接
RESOURCES=$(curl -s "https://192.168.60.52/chat/" -k | grep -oE '(src|href)="[^"]*"' | sed 's/.*="//' | sed 's/"//' | grep -E '\.(js|css|ico|png|webmanifest|json|xml)' | sort | uniq)

TOTAL_RESOURCES=0
SUCCESS_COUNT=0
FAILED_RESOURCES=()

echo "   测试资源列表:"

while IFS= read -r resource; do
    if [ -n "$resource" ]; then
        TOTAL_RESOURCES=$((TOTAL_RESOURCES + 1))
        
        # 构建完整URL
        if [[ "$resource" == /* ]]; then
            FULL_URL="https://192.168.60.52$resource"
        else
            FULL_URL="https://192.168.60.52/chat/$resource"
        fi
        
        # 测试资源
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$FULL_URL" -k)
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "   ✅ $resource (HTTP $HTTP_CODE)"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            echo "   ❌ $resource (HTTP $HTTP_CODE)"
            FAILED_RESOURCES+=("$resource")
        fi
    fi
done <<< "$RESOURCES"

echo ""
echo "📊 测试结果统计:"
echo "   总资源数: $TOTAL_RESOURCES"
echo "   成功加载: $SUCCESS_COUNT"
echo "   失败数量: $((TOTAL_RESOURCES - SUCCESS_COUNT))"

if [ $SUCCESS_COUNT -eq $TOTAL_RESOURCES ]; then
    echo "   🎉 所有资源都可以正常访问！"
else
    echo "   ⚠️  以下资源仍然无法访问:"
    for failed in "${FAILED_RESOURCES[@]}"; do
        echo "      - $failed"
    done
fi

echo ""
echo "2. 特定问题资源检查..."

# 检查之前报错的特定资源
PROBLEM_RESOURCES=(
    "/chat/favicon-32x32.ico?v=1"
    "/chat/apple-touch-icon.png?v=1"
    "/chat/manifest.webmanifest"
    "/chat/_next/static/css/967104e9fd9cfb93.css"
    "/chat/_next/static/chunks/webpack-b35e42786ad727d1.js"
)

for resource in "${PROBLEM_RESOURCES[@]}"; do
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://192.168.60.52$resource" -k)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "   ✅ $resource"
    else
        echo "   ❌ $resource (HTTP $HTTP_CODE)"
    fi
done

echo ""
echo "3. MIME类型验证..."

# 检查关键资源的MIME类型
echo "   CSS MIME类型:"
CSS_MIME=$(curl -s -I "https://192.168.60.52/chat/_next/static/css/967104e9fd9cfb93.css" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $CSS_MIME"

echo "   JS MIME类型:"
JS_MIME=$(curl -s -I "https://192.168.60.52/chat/_next/static/chunks/webpack-b35e42786ad727d1.js" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $JS_MIME"

echo "   ICO MIME类型:"
ICO_MIME=$(curl -s -I "https://192.168.60.52/chat/favicon-32x32.ico?v=1" -k | grep -i "content-type" | cut -d' ' -f2-)
echo "      $ICO_MIME"

echo ""
echo "4. 路径重写验证..."

# 检查是否还有未重写的路径
UNREWRITTEN=$(curl -s "https://192.168.60.52/chat/" -k | grep -oE '(src|href)="/_next/static/[^"]*"' | wc -l)
REWRITTEN=$(curl -s "https://192.168.60.52/chat/" -k | grep -oE '(src|href)="/chat/_next/static/[^"]*"' | wc -l)

echo "   未重写的路径: $UNREWRITTEN"
echo "   已重写的路径: $REWRITTEN"

if [ "$UNREWRITTEN" -eq 0 ] && [ "$REWRITTEN" -gt 0 ]; then
    echo "   ✅ 路径重写完全成功"
else
    echo "   ❌ 路径重写不完整"
fi

echo ""
echo "🎯 最终检查完成！"

if [ $SUCCESS_COUNT -eq $TOTAL_RESOURCES ] && [ "$UNREWRITTEN" -eq 0 ]; then
    echo ""
    echo "🎉 恭喜！LobeChat集成已完全修复："
    echo "   ✅ 所有静态资源都可以正常访问"
    echo "   ✅ 路径重写完全成功"
    echo "   ✅ MIME类型正确设置"
    echo "   ✅ 不再有404错误"
    echo ""
    echo "🌐 现在可以正常使用LobeChat了！"
    echo "   主平台: https://192.168.60.52/#chat"
    echo "   测试页面: https://192.168.60.52/test-lobechat.html"
else
    echo ""
    echo "⚠️  仍有一些问题需要解决，请检查上述失败的资源。"
fi
