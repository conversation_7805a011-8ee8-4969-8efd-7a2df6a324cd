# ===================================================================
# YNNX AI Platform - 环境变量配置
# 复制此文件为 .env 并根据实际环境修改相应的值
# ===================================================================

# ===== 应用基础配置 =====
NODE_ENV=production

# 跨域配置
CORS_ORIGIN=*

# MCP服务版本

# ===== 服务端口配置 =====

# LDAP认证服务配置
LDAP_AUTH_PORT=3002

# ===== 前端API配置 =====
# LDAP认证API服务器
VITE_LDAP_API_URL=/api/ldap

# 聊天服务API

# LiteLLM前端API
VITE_LITELLM_API_BASE=/api/litellm

# LobeChat 智能对话配置
VITE_LOBECHAT_BASE_URL=/chat/
VITE_LOBECHAT_PROXY_URL=/chat/

# ===== LDAP 认证配置 =====
# LDAP基础配置
LDAP_DEFAULT_HOST=*************
LDAP_DEFAULT_PORT=11389
LDAP_DEFAULT_ENVIRONMENT=DEVVDI_ENV

# LDAP连接配置
LDAP_CONNECTION_TIMEOUT=5000
LDAP_SEARCH_TIMEOUT=10000
LDAP_MAX_RETRIES=3

# LDAP测试端口配置
LDAP_TEST_PORT=11389
LDAPS_TEST_PORT=636
LDAP_GC_PORT=3268
LDAPS_GC_PORT=3269

# ===== LDAP 环境配置 =====
# 240.10云桌面环境 (DEVVDI)
LDAP_DEVVDI_ENV_URL=ldap://*************:11389
LDAP_DEVVDI_ENV_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
LDAP_DEVVDI_ENV_BIND_DN=
LDAP_DEVVDI_ENV_BIND_PASSWORD=
LDAP_DEVVDI_ENV_USER_SEARCH_BASE=DC=DEVVDI,DC=YNRCC,DC=COM
LDAP_DEVVDI_ENV_USER_FILTER=(userPrincipalName={{username}}@DEVVDI.YNRCC.COM)
LDAP_DEVVDI_ENV_USER_DN_PATTERN={{username}}@DEVVDI.YNRCC.COM
LDAP_DEVVDI_ENV_USER_DOMAIN=@DEVVDI.YNRCC.COM
LDAP_DEVVDI_ENV_USE_DIRECT_BIND=true
LDAP_DEVVDI_ENV_NAME=240.10云桌面环境
LDAP_DEVVDI_ENV_DESCRIPTION=DEVVDI Active Directory环境 - 端口389

# 242.2云桌面环境 (VDI)
LDAP_VDI_ENV_URL=ldap://*************:12389
LDAP_VDI_ENV_BASE_DN=DC=VDI,DC=YNNX,DC=COM
LDAP_VDI_ENV_BIND_DN=
LDAP_VDI_ENV_BIND_PASSWORD=
LDAP_VDI_ENV_USER_SEARCH_BASE=DC=VDI,DC=YNNX,DC=COM
LDAP_VDI_ENV_USER_FILTER=(userPrincipalName={{username}}@VDI.YNNX.COM)
LDAP_VDI_ENV_USER_DN_PATTERN={{username}}@VDI.YNNX.COM
LDAP_VDI_ENV_USER_DOMAIN=@VDI.YNNX.COM
LDAP_VDI_ENV_USE_DIRECT_BIND=true
LDAP_VDI_ENV_NAME=242.2云桌面环境
LDAP_VDI_ENV_DESCRIPTION=VDI Active Directory环境 - 端口389

# ===== LiteLLM 代理服务配置 =====
# 注意：这是后端服务配置，不要与前端VITE_LITELLM_API_BASE混淆
LITELLM_BACKEND_URL=http://*************:14000
LITELLM_MASTER_KEY=sk-ynnx-llm-20250530

# ===== LLM 模型配置 =====
# OpenAI配置
OPENAI_API_KEY=sk-ynnx-llm-20250530
OPENAI_MODEL=qwen3-235b-a22b
OPENAI_BASE_URL=http://*************:14000/v1

# Anthropic Claude配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Azure OpenAI配置
AZURE_OPENAI_ENDPOINT=https://your-instance.openai.azure.com
AZURE_OPENAI_API_KEY=your_azure_api_key_here

# ===== LLM 功能开关 =====
ENABLE_OPENAI=true
ENABLE_ANTHROPIC=false
ENABLE_AZURE=false

# ===== 性能配置 =====
# LLM调用配置
LLM_TIMEOUT=60000              # LLM API超时时间(毫秒) - 30秒
LLM_MAX_TOKENS=4000           # 最大生成token数
LLM_TEMPERATURE=0.7           # 生成文本的随机性

# API重试配置
API_RETRY_COUNT=2              # API重试次数
API_RETRY_DELAY=1000           # API重试延迟(毫秒)

# env for doc
DOC_LLM_API_URL = http://*************:14000/v1
DOC_LLM_MODEL = qwen3-235b-a22b
# ===================================================================
# 部署说明:
# 1. 复制此文件为 .env: cp env.example .env
# 2. 根据实际环境修改相应的配置值
# 3. LDAP配置完全通过环境变量管理，无需额外的JSON配置文件
# 4. 生产环境请配置真实的API密钥
# =================================================================== 