# API地址显示修改说明

## 修改目标
将前端页面中显示的LLM API地址从 `/api/litellm` 改为显示 `.env` 文件中的 `LITELLM_BACKEND_URL` 变量值。

## 修改内容

### 1. 新增配置函数 (`src/config/apiConfig.js`)
- 添加了 `getLiteLLMBackendUrl()` 函数
- 该函数读取 `LITELLM_BACKEND_URL` 或 `VITE_LITELLM_BACKEND_URL` 环境变量
- 如果环境变量未设置，则使用默认值 `https://*************:14000`

```javascript
// 获取LiteLLM后端服务地址（用于显示给用户）
export const getLiteLLMBackendUrl = () => {
  return getEnvVar('LITELLM_BACKEND_URL') || getEnvVar('VITE_LITELLM_BACKEND_URL') || 'https://*************:14000';
};
```

### 2. 修改的组件文件

#### `src/components/APIKeySection.jsx`
- 导入 `getLiteLLMBackendUrl` 函数
- 修改连接状态显示：`LLM API: {getLiteLLMBackendUrl()}`
- 修改密钥使用说明中的API地址显示

#### `src/components/FeaturesGuideSection.jsx`
- 导入 `getLiteLLMBackendUrl` 函数
- 修改数据来源显示：`数据来源: LLM API ({getLiteLLMBackendUrl()})`

#### `src/components/LiteLLMConfig.jsx`
- 导入 `getLiteLLMBackendUrl` 函数
- 修改服务地址显示：`服务地址: {getLiteLLMBackendUrl()}`
- 修改配置说明中的服务地址

#### `src/components/DownloadsSection.jsx`
- 导入 `getLiteLLMBackendUrl` 函数
- 修改API配置信息中的地址显示
- 清理了不再使用的 `getLiteLLMApiBase` 导入

## 环境变量配置

### 当前 `.env` 文件中的相关配置：
```bash
# 后端服务地址（用于显示给用户）
LITELLM_BACKEND_URL=https://*************:14000

# 前端API路径（用于实际API调用）
VITE_LITELLM_API_BASE=/api/litellm
```

## 修改前后对比

### 修改前：
- 显示：`已连接 LLM API: /api/litellm`
- 来源：`getLiteLLMApiBase()` 函数
- 值：`/api/litellm`（相对路径）

### 修改后：
- 显示：`已连接 LLM API: https://*************:14000`
- 来源：`getLiteLLMBackendUrl()` 函数
- 值：`https://*************:14000`（完整URL）

## 影响范围

### 修改的显示位置：
1. **API密钥管理页面**：连接状态和使用说明
2. **功能指南页面**：数据来源说明
3. **LiteLLM配置页面**：服务状态和配置说明
4. **下载页面**：插件API配置信息

### 不受影响的功能：
- 实际的API调用仍使用 `getLiteLLMApiBase()` 返回的相对路径
- 后端服务配置不受影响
- 用户认证和密钥管理功能正常

## 测试验证

1. 启动开发服务器：`npm run dev`
2. 访问相关页面检查显示是否正确
3. 确认API功能正常工作
4. 验证环境变量读取正确

## 注意事项

1. 该修改仅影响前端显示，不影响实际API调用逻辑
2. 如需修改后端服务地址，请更新 `.env` 文件中的 `LITELLM_BACKEND_URL` 变量
3. 生产环境部署时需要确保环境变量正确配置
