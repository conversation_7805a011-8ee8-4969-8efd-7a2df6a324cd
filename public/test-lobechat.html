<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LobeChat 集成测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #444;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background: #2d5a2d;
            border: 1px solid #4a8a4a;
        }
        .status.error {
            background: #5a2d2d;
            border: 1px solid #8a4a4a;
        }
        .status.info {
            background: #2d4a5a;
            border: 1px solid #4a7a8a;
        }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0052a3;
        }
        .log {
            background: #1a1a1a;
            border: 1px solid #444;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LobeChat 集成测试页面</h1>
        
        <div class="test-info">
            <h2>测试信息</h2>
            <div id="status-container">
                <div class="status info">
                    <strong>测试目标:</strong> 验证 LobeChat 通过 nginx 代理的集成是否正常工作
                </div>
                <div class="status info">
                    <strong>代理路径:</strong> /chat/
                </div>
                <div class="status info">
                    <strong>目标服务:</strong> http://*************:3210
                </div>
            </div>
            
            <div>
                <button onclick="testDirectAccess()">测试直接访问</button>
                <button onclick="testProxyAccess()">测试代理访问</button>
                <button onclick="testStaticResources()">测试静态资源</button>
                <button onclick="loadIframe()">加载 iframe</button>
                <button onclick="clearLog()">清除日志</button>
            </div>
            
            <div class="log" id="log"></div>
        </div>
        
        <div class="iframe-container">
            <iframe id="lobechat-iframe" title="LobeChat 测试"></iframe>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testDirectAccess() {
            log('测试直接访问 LobeChat 服务...');
            try {
                const response = await fetch('http://*************:3210', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                log('直接访问测试完成（由于CORS限制，无法获取详细状态）', 'info');
            } catch (error) {
                log(`直接访问失败: ${error.message}`, 'error');
            }
        }

        async function testProxyAccess() {
            log('测试通过代理访问 LobeChat...');
            try {
                const response = await fetch('/chat/', { method: 'HEAD' });
                if (response.ok) {
                    log(`代理访问成功: ${response.status} ${response.statusText}`, 'success');
                    log(`Content-Type: ${response.headers.get('content-type')}`, 'info');
                } else {
                    log(`代理访问失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`代理访问错误: ${error.message}`, 'error');
            }
        }

        async function testStaticResources() {
            log('测试静态资源访问...');
            const testUrls = [
                '/chat/_next/static/css/967104e9fd9cfb93.css',
                '/chat/_next/static/chunks/webpack-b35e42786ad727d1.js',
                '/chat/favicon.ico'
            ];

            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    const contentType = response.headers.get('content-type');
                    if (response.ok) {
                        log(`✓ ${url} - ${response.status} (${contentType})`, 'success');
                    } else {
                        log(`✗ ${url} - ${response.status} ${response.statusText}`, 'error');
                    }
                } catch (error) {
                    log(`✗ ${url} - ${error.message}`, 'error');
                }
            }
        }

        function loadIframe() {
            log('加载 LobeChat iframe...');
            const iframe = document.getElementById('lobechat-iframe');

            // 设置模拟认证头部
            const authHeaders = {
                'X-Auth-User': 'testuser',
                'X-Auth-Email': '<EMAIL>',
                'X-Auth-Name': 'Test User'
            };

            // 添加认证头部到页面
            Object.entries(authHeaders).forEach(([key, value]) => {
                let meta = document.querySelector(`meta[name="${key}"]`);
                if (!meta) {
                    meta = document.createElement('meta');
                    meta.name = key;
                    document.head.appendChild(meta);
                }
                meta.content = value;
            });

            let loadStartTime = Date.now();

            iframe.onload = function() {
                const loadTime = Date.now() - loadStartTime;
                log(`iframe 加载完成 (${loadTime}ms)`, 'success');

                // 检查iframe内容
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            const title = iframeDoc.title;
                            log(`iframe 页面标题: ${title}`, 'info');

                            // 检查是否有JavaScript错误
                            const scripts = iframeDoc.querySelectorAll('script[src]');
                            log(`iframe 中发现 ${scripts.length} 个脚本文件`, 'info');
                        }
                    } catch (e) {
                        log('无法访问iframe内容（跨域限制）', 'info');
                    }
                }, 2000);
            };

            iframe.onerror = function() {
                log('iframe 加载失败', 'error');
            };

            iframe.src = '/chat/';
        }

        // 页面加载完成后自动运行一些测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testProxyAccess();
                setTimeout(() => {
                    testStaticResources();
                }, 1000);
            }, 500);
        };

        // 监听 iframe 消息
        window.addEventListener('message', function(event) {
            log(`收到 iframe 消息: ${JSON.stringify(event.data)}`, 'info');
        });
    </script>
</body>
</html>
