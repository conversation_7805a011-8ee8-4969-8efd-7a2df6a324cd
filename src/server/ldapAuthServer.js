// LDAP认证后端服务器
// 真正连接LDAP服务器进行认证

// 加载环境变量
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载项目根目录的.env文件
dotenv.config({ path: path.join(__dirname, '../../.env') });

import express from 'express';
import cors from 'cors';
import ldap from 'ldapjs';
import process from 'process';
import ldapConfig from './ldapConfig.js';

const app = express();
const PORT = process.env.LDAP_AUTH_PORT || 3002;

// 全局错误处理 - 防止未处理的错误导致进程退出
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message);
  console.error('堆栈信息:', error.stack);
  // 不要退出进程，而是记录错误并继续运行
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
  // 不要退出进程，而是记录错误并继续运行
});

// CORS 配置函数 - 与nlweb保持一致
const getCorsOrigin = () => {
  // 优先使用环境变量明确指定的CORS源
  if (process.env.CORS_ORIGIN) {
    // 如果包含多个源，分割成数组
    const origins = process.env.CORS_ORIGIN.split(',').map(origin => origin.trim());
    return origins.length === 1 ? origins[0] : origins;
  }
  
  // 根据环境自动配置
  switch (process.env.NODE_ENV) {
    case 'production':
      return process.env.PROD_FRONTEND_URL || 'http://localhost:5173' // 内网环境：请通过环境变量配置实际地址;
    case 'test':
      return process.env.TEST_FRONTEND_URL || 'http://*************:5173';
    default:
      return process.env.DEV_FRONTEND_URL || 'http://localhost:5173';
  }
};

// 中间件配置
app.use(cors({
  origin: getCorsOrigin(),
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization',
    'Accept',
    'Accept-Language',
    'Cache-Control',
    'User-Agent',
    'Referer'
  ],
  credentials: false,
  maxAge: 86400 // 预检请求缓存24小时
}));
// 增加请求体大小限制
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// 健康检查API - 添加LDAP服务器的健康检查
app.get('/health', async (req, res) => {
  try {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 健康检查请求`);
    
    // 基本服务健康状态
    const healthStatus = {
      status: 'healthy',
      timestamp,
      service: 'LDAP认证服务',
      port: PORT,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development'
    };

    // 如果有LDAP配置，测试LDAP连接
    try {
      const config = ldapConfig.getCurrentConfig();
      if (config && config.url) {
        const ldapClient = ldap.createClient({
          url: config.url,
          timeout: 2000,
          connectTimeout: 2000
        });

        // 添加错误处理监听器，防止未处理的错误事件
        ldapClient.on('error', (error) => {
          console.warn(`[健康检查] LDAP连接错误: ${error.message}`);
          // 不要抛出错误，只是记录
        });

        // 简单的连接测试
        await new Promise((resolve, _reject) => {
          const timeout = setTimeout(() => {
            healthStatus.ldap = { status: 'timeout', server: config.url };
            resolve(); // 超时不算失败，只是标记状态
          }, 2000);

          ldapClient.on('connect', () => {
            clearTimeout(timeout);
            healthStatus.ldap = { status: 'connected', server: config.url };
            try {
              ldapClient.unbind();
            } catch {
              // 忽略unbind错误
            }
            resolve();
          });

          ldapClient.on('error', (error) => {
            clearTimeout(timeout);
            healthStatus.ldap = { status: 'error', server: config.url, error: error.message };
            resolve(); // 连接错误不影响服务整体健康状态
          });
        });
      } else {
        healthStatus.ldap = { status: 'no_config' };
      }
    } catch (ldapError) {
      console.warn(`[健康检查] LDAP测试错误: ${ldapError.message}`);
      healthStatus.ldap = { status: 'test_error', error: ldapError.message };
    }

    res.json(healthStatus);
  } catch (error) {
    console.error(`[健康检查] 失败: ${error.message}`);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// 兼容旧版本的LDAP_CONFIG - 现在从配置文件读取
const LDAP_CONFIG = {
  get primary() {
    return ldapConfig.getCurrentConfig();
  }
};

// 创建安全的LDAP客户端连接
function createSafeClient(config) {
  const client = ldap.createClient({
    url: config.url,
    timeout: config.timeout || 5000,
    connectTimeout: config.connectTimeout || 5000
  });

  // 添加错误事件监听器，防止未处理的错误事件导致进程退出
  client.on('error', (error) => {
    console.warn(`[LDAP客户端] 连接错误: ${error.message}`);
    // 不要抛出错误，只是记录日志
  });

  client.on('close', () => {
    console.log('[LDAP客户端] 连接已关闭');
  });

  client.on('timeout', () => {
    console.warn('[LDAP客户端] 连接超时');
  });

  return client;
}

// 安全关闭LDAP客户端连接 (异步版本)
async function safeUnbind(client) {
  return new Promise((resolve) => {
    try {
      if (client && typeof client.unbind === 'function') {
        client.unbind(() => {
          resolve();
        });
        
        // 设置超时，防止unbind卡住
        setTimeout(() => {
          resolve();
        }, 1000);
      } else {
        resolve();
      }
    } catch (error) {
      console.warn(`[LDAP客户端] unbind错误: ${error.message}`);
      resolve(); // 即使unbind失败也要继续
    }
  });
}

// 安全关闭LDAP客户端连接 (同步版本)
function safeUnbindSync(client) {
  try {
    if (client && typeof client.unbind === 'function') {
      client.unbind();
    }
  } catch (error) {
    console.warn(`[LDAP客户端] unbind错误: ${error.message}`);
    // 即使unbind失败也要继续
  }
}

// 处理AD用户名格式
function processUsername(username) {
  if (username.includes('@')) {
    return username.split('@')[0];
  }
  if (username.includes('\\')) {
    return username.split('\\')[1];
  }
  return username;
}

// LDAP认证API
app.post('/api/ldap/authenticate', async (req, res) => {
  const { username, password, ldapEnvironment } = req.body;

  if (!username || !password) {
    return res.status(400).json({
      success: false,
      error: '用户名和密码不能为空'
    });
  }

  const processedUsername = processUsername(username);
  
  // 特殊用户认证：admin用户绕过LDAP认证，但适应选择的环境
  if (processedUsername === 'admin' && password === 'Admin@PassW0rd') {
    console.log(`[特殊认证] 检测到admin用户，绕过LDAP认证`);
    
    // 根据用户选择的环境获取相应的配置信息
    let selectedConfig;
    let selectedEnvironmentName;
    
    if (ldapEnvironment && ldapConfig.isValidEnvironment(ldapEnvironment)) {
      selectedConfig = ldapConfig.getEnvironmentConfig(ldapEnvironment);
      selectedEnvironmentName = selectedConfig.name;
      console.log(`[特殊认证] admin用户选择环境: ${ldapEnvironment}`);
    } else {
      selectedConfig = LDAP_CONFIG.primary;
      selectedEnvironmentName = ldapConfig.getCurrentEnvironment();
      console.log(`[特殊认证] admin用户使用默认环境: ${selectedEnvironmentName}`);
    }
    
    // 构造适应选择环境的admin用户信息
    const adminUserInfo = {
      username: 'admin',
      name: `管理员账户(${selectedEnvironmentName})`,
      email: `admin@${selectedConfig.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
      phone: '',
      userPrincipalName: `admin@${selectedConfig.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
      ldapDN: `cn=admin,${selectedConfig.baseDN}`,
      ldapServer: selectedConfig.url,
      ldapEnvironment: selectedEnvironmentName,
      lastLogin: new Date().toISOString(),
      isSpecialUser: true // 标识这是特殊用户
    };

    console.log(`[特殊认证] admin用户认证成功，环境: ${selectedEnvironmentName}`);
    
    return res.json({
      success: true,
      user: adminUserInfo,
      ldapServer: selectedConfig.url
    });
  }
  
  // 根据请求参数选择LDAP环境，如果未指定则使用当前默认环境
  let config;
  if (ldapEnvironment && ldapConfig.isValidEnvironment(ldapEnvironment)) {
    config = ldapConfig.getEnvironmentConfig(ldapEnvironment);
    console.log(`[LDAP认证] 使用指定环境: ${ldapEnvironment}`);
  } else {
    config = LDAP_CONFIG.primary;
    console.log(`[LDAP认证] 使用默认环境: ${ldapConfig.getCurrentEnvironment()}`);
  }

  console.log(`[LDAP认证] 尝试认证用户: ${processedUsername}`);
  console.log(`[LDAP认证] 连接服务器: ${config.url}`);

  try {
    // 创建安全的LDAP客户端
    const client = createSafeClient(config);

    // Promise包装LDAP操作
    const authenticateUser = () => {
      return new Promise((resolve, reject) => {
        // 1. 绑定操作
        const performBind = () => {
          if (config.bindDN && config.bindPassword) {
            console.log(`[LDAP认证] 使用绑定用户: ${config.bindDN}`);
            client.bind(config.bindDN, config.bindPassword, (err) => {
              if (err) {
                console.warn(`[LDAP认证] 绑定用户失败: ${err.message}，跳过绑定直接尝试用户认证`);
                // 绑定失败不抛出错误，而是直接尝试用户认证
                tryDirectAuthentication();
                return;
              }
              console.log(`[LDAP认证] 绑定用户成功`);
              tryDirectAuthentication();
            });
          } else {
            console.log(`[LDAP认证] 使用匿名绑定`);
            tryDirectAuthentication();
          }
        };

        // 2. 直接尝试用户认证（跳过搜索步骤）
        const tryDirectAuthentication = () => {
          let possibleUserDNs = [];
          
          // 统一处理：优先使用配置的userDNPattern，然后尝试其他格式
          if (config.useDirectBind && config.userDNPattern) {
            // 使用配置的userDNPattern（支持所有环境）
            console.log(`[LDAP认证] 使用配置的userDNPattern进行认证`);
            const userDN = config.userDNPattern.replace('{{username}}', processedUsername);
            possibleUserDNs = [
              userDN, // 首选：使用配置的DN模式
              `${processedUsername}${config.userDomain}`, // 备用：使用配置的域
              `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.').toUpperCase()}` // 备用：从baseDN生成域
            ];
          } else {
            // 通用环境处理：构造可能的用户DN格式
            console.log(`[LDAP认证] 使用通用DN格式进行认证`);
            possibleUserDNs = [
              // UPN格式 - 优先尝试
              `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.').toUpperCase()}`,
              // 如果有配置的用户域，也尝试使用
              config.userDomain ? `${processedUsername}${config.userDomain}` : null,
              // 传统DN格式
              `cn=${processedUsername},${config.userSearchBase}`,
              `uid=${processedUsername},${config.userSearchBase}`,
              `sAMAccountName=${processedUsername},${config.userSearchBase}`,
              `cn=${processedUsername},${config.baseDN}`,
              `uid=${processedUsername},${config.baseDN}`,
              `sAMAccountName=${processedUsername},${config.baseDN}`
            ].filter(dn => dn !== null); // 过滤掉null值
          }

          let currentDNIndex = 0;

          const tryNextDN = () => {
            if (currentDNIndex >= possibleUserDNs.length) {
              console.error(`[LDAP认证] 尝试了所有可能的用户DN格式都无法认证 ${processedUsername}`);
              console.log(`[LDAP认证] 尝试过的DN:`, possibleUserDNs);
              reject(new Error('用户名或密码错误'));
              return;
            }

            const userDN = possibleUserDNs[currentDNIndex];
            console.log(`[LDAP认证] 尝试直接认证 DN: ${userDN} (${currentDNIndex + 1}/${possibleUserDNs.length})`);

            // 创建新的安全客户端连接来验证用户
            const authClient = createSafeClient(config);

            authClient.bind(userDN, password, (bindErr) => {
              if (bindErr) {
                console.log(`[LDAP认证] DN ${userDN} 认证失败: ${bindErr.message}`);
                safeUnbindSync(authClient);
                currentDNIndex++;
                tryNextDN();
                return;
              }

              console.log(`[LDAP认证] ✅ 用户认证成功: ${userDN}`);

              // 如果使用UPN格式认证成功，直接返回基本用户信息，无需搜索
              const isUPN = userDN.includes('@');
              if (isUPN) {
                // UPN格式认证成功，直接构造用户信息并返回
                safeUnbindSync(authClient);
                console.log(`[LDAP认证] UPN认证成功，跳过搜索直接返回用户信息`);
                
                const userInfo = {
                  username: processedUsername,
                  name: processedUsername,
                  email: userDN, // 使用UPN作为邮箱
                  phone: '',
                  userPrincipalName: userDN,
                  ldapDN: userDN,
                  ldapServer: config.url,
                  ldapEnvironment: config.name,
                  lastLogin: new Date().toISOString()
                };
                resolve(userInfo);
                return;
              } else {
                // DN格式认证成功，直接获取详细信息
                getUserDetails(authClient, userDN);
              }
            });
          };

          // 获取用户详细信息（DN格式）
          const getUserDetails = (authClient, userDN) => {
            // 获取用户详细信息
            authClient.search(userDN, {
              scope: 'base',
              filter: '(objectclass=*)',
              attributes: ['cn', 'sAMAccountName', 'uid', 'mail', 'telephoneNumber', 'userPrincipalName']
            }, (searchErr, searchRes) => {
              safeUnbindSync(authClient);

              if (searchErr) {
                console.warn(`[LDAP认证] 获取用户详情失败: ${searchErr.message}`);
                // 即使获取详情失败，认证也成功了，返回基本信息
                const basicUserInfo = {
                  username: processedUsername,
                  name: processedUsername,
                  email: `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
                  phone: '',
                  userPrincipalName: `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
                  ldapDN: userDN,
                  ldapServer: config.url,
                  ldapEnvironment: config.name,
                  lastLogin: new Date().toISOString()
                };
                resolve(basicUserInfo);
                return;
              }

              let userEntry = null;
              searchRes.on('searchEntry', (entry) => {
                userEntry = entry.object;
              });

              searchRes.on('error', (detailErr) => {
                console.warn(`[LDAP认证] 获取用户详情过程出错: ${detailErr.message}`);
              });

              searchRes.on('end', () => {
                if (userEntry) {
                  // 整理用户信息
                  const userInfo = {
                    username: userEntry.uid || processedUsername,
                    name: userEntry.cn || userEntry.uid || processedUsername,
                    email: userEntry.mail || `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
                    phone: userEntry.telephoneNumber || '',
                    userPrincipalName: userEntry.userPrincipalName || `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
                    ldapDN: userDN,
                    ldapServer: config.url,
                    ldapEnvironment: config.name,
                    lastLogin: new Date().toISOString()
                  };
                  resolve(userInfo);
                } else {
                  // 认证成功但无法获取详情，返回基本信息
                  const basicUserInfo = {
                    username: processedUsername,
                    name: processedUsername,
                    email: `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
                    phone: '',
                    userPrincipalName: `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
                    ldapDN: userDN,
                    ldapServer: config.url,
                    ldapEnvironment: config.name,
                    lastLogin: new Date().toISOString()
                  };
                  resolve(basicUserInfo);
                }
              });
            });
          };

          // 开始尝试直接认证
          tryNextDN();
        };

        // 3. 验证用户密码 (当前未使用，保留以备后续扩展)
        // eslint-disable-next-line no-unused-vars
        const validateUserPassword = (userEntry) => {
          const userDN = userEntry.distinguishedName;
          console.log(`[LDAP认证] 验证用户密码: ${userDN}`);

          // 创建新的客户端连接来验证用户密码
          const authClient = createSafeClient(config);

          authClient.bind(userDN, password, (bindErr) => {
            safeUnbindSync(authClient);

            if (bindErr) {
              console.log(`[LDAP认证] 密码验证失败: ${bindErr.message}`);
              reject(new Error('用户名或密码错误'));
              return;
            }

            console.log(`[LDAP认证] 用户认证成功`);

            // 整理用户信息
            const userInfo = {
              username: userEntry.uid || processedUsername,
              name: userEntry.cn || userEntry.uid || processedUsername,
              email: userEntry.mail || `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
              phone: userEntry.telephoneNumber || '',
              userPrincipalName: userEntry.userPrincipalName || `${processedUsername}@${config.baseDN.replace(/dc=/g, '').replace(/,/g, '.')}`,
              ldapDN: userDN,
              ldapServer: config.url,
              ldapEnvironment: config.name,
              lastLogin: new Date().toISOString()
            };

            resolve(userInfo);
          });
        };

        // 开始认证流程
        performBind();
      });
    };

    // 执行认证
    const authResult = await authenticateUser();

    // 安全关闭连接
    await safeUnbind(client);

    console.log(`[LDAP认证] 认证成功: ${authResult.name}`);

    res.json({
      success: true,
      user: authResult,
      ldapServer: '主LDAP服务器'
    });

  } catch (error) {
    console.error(`[LDAP认证] 认证失败:`, error.message);
    
    res.status(401).json({
      success: false,
      error: error.message
    });
  }
});

// 测试LDAP连接API
app.get('/api/ldap/test', async (req, res) => {
  const config = LDAP_CONFIG.primary;
  
  try {
    const client = createSafeClient(config);

    const testConnection = () => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('连接超时'));
        }, 3000);

        client.on('connect', () => {
          clearTimeout(timeout);
          safeUnbind(client);
          resolve(true);
        });

        client.on('error', (err) => {
          clearTimeout(timeout);
          reject(err);
        });
      });
    };

    await testConnection();

      res.json({
    success: true,
    message: '连接成功',
    server: config.url
  });

} catch (error) {
  res.status(500).json({
    success: false,
    error: error.message,
    server: config.url
  });
}
});

// 获取当前LDAP配置API
app.get('/api/ldap/config', async (req, res) => {
  try {
    const config = ldapConfig.getCurrentConfig();
    const configInfo = ldapConfig.getConfigInfo(config);
    
    res.json({
      success: true,
      ...configInfo
    });
  } catch (error) {
    console.error('[获取配置] 失败:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取所有LDAP环境列表API
app.get('/api/ldap/environments', async (req, res) => {
  try {
    const environments = ldapConfig.getAllEnvironments();
    const currentEnv = ldapConfig.getCurrentEnvironment();
    
    res.json({
      success: true,
      environments: environments,
      current: currentEnv
    });
  } catch (error) {
    console.error('[获取环境列表] 失败:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      environments: []
    });
  }
});

// 测试多种LDAP连接方式
app.get('/api/ldap/test-all', async (req, res) => {
  // eslint-disable-next-line no-unused-vars
  const config = LDAP_CONFIG.primary;
  const host = process.env.LDAP_PRIMARY_HOST || process.env.LDAP_DEFAULT_HOST || 'localhost';
  
  // 从环境变量获取测试端口，如果没有设置则使用标准端口
  const testPorts = {
    ldap: process.env.LDAP_TEST_PORT || '389',
    ldaps: process.env.LDAPS_TEST_PORT || '636',
    ldap_gc: process.env.LDAP_GC_PORT || '3268',
    ldaps_gc: process.env.LDAPS_GC_PORT || '3269'
  };
  
        const testUrls = [
    `ldap://${host}:${testPorts.ldap}`,
    `ldaps://${host}:${testPorts.ldaps}`,
    `ldap://${host}:${testPorts.ldap_gc}`,  // AD全局目录
    `ldaps://${host}:${testPorts.ldaps_gc}`  // AD全局目录 SSL
  ];
  
  const results = [];
  
  for (const url of testUrls) {
    try {
      console.log(`[连接测试] 尝试连接: ${url}`);
      
      const client = ldap.createClient({
        url: url,
        timeout: 3000,
        connectTimeout: 3000
      });

      // 添加错误处理
      client.on('error', (error) => {
        console.warn(`[连接测试] 客户端错误: ${error.message}`);
      });

      const testConnection = () => {
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('连接超时'));
          }, 3000);

          client.on('connect', () => {
            clearTimeout(timeout);
            safeUnbind(client);
            resolve(true);
          });

          client.on('error', (err) => {
            clearTimeout(timeout);
            reject(err);
          });
        });
      };

      await testConnection();
      
      results.push({
        url: url,
        success: true,
        message: '连接成功'
      });
      
      console.log(`[连接测试] ${url} - 成功`);

    } catch (error) {
      results.push({
        url: url,
        success: false,
        error: error.message
      });
      
      console.log(`[连接测试] ${url} - 失败: ${error.message}`);
    }
  }
  
  res.json({
    success: true,
    testResults: results,
    workingUrls: results.filter(r => r.success).map(r => r.url)
  });
});

// LDAP用户搜索调试API
app.get('/api/ldap/find-user/:username', async (req, res) => {
  const { username } = req.params;
  const config = LDAP_CONFIG.primary;
  const processedUsername = processUsername(username);
  
  console.log(`[用户查找] 开始搜索用户: ${processedUsername}`);
  
  try {
    const client = createSafeClient({
      ...config,
      timeout: 10000,
      connectTimeout: 10000
    });

    const findUser = () => {
      return new Promise((resolve, reject) => {
        // 绑定操作
        const performBind = () => {
          if (config.bindDN && config.bindPassword) {
            client.bind(config.bindDN, config.bindPassword, (err) => {
              if (err) {
                reject(new Error('LDAP绑定失败'));
                return;
              }
              searchInAllPossiblePaths();
            });
          } else {
            searchInAllPossiblePaths();
          }
        };

        // 在所有可能的路径中搜索用户
        const searchInAllPossiblePaths = () => {
          const searchPaths = [
            // 基于您配置的各种可能路径
            'CN=维护,OU=USER,OU=YNRCC,OU=DEVELOP,OU=DEVVDI,DC=DEVVDI,DC=YNRCC,DC=COM',
            'OU=USER,OU=YNRCC,OU=DEVELOP,OU=DEVVDI,DC=DEVVDI,DC=YNRCC,DC=COM',
            'OU=YNRCC,OU=DEVELOP,OU=DEVVDI,DC=DEVVDI,DC=YNRCC,DC=COM',
            'OU=DEVELOP,OU=DEVVDI,DC=DEVVDI,DC=YNRCC,DC=COM',
            'OU=DEVVDI,DC=DEVVDI,DC=YNRCC,DC=COM',
            'DC=DEVVDI,DC=YNRCC,DC=COM',
            'DC=YNRCC,DC=COM',
            'DC=COM',
            // AD默认路径
            'CN=Users,DC=DEVVDI,DC=YNRCC,DC=COM',
            'OU=Users,DC=DEVVDI,DC=YNRCC,DC=COM'
          ];

          const results = [];
          let completedSearches = 0;

          const searchFilters = [
            `(sAMAccountName=${processedUsername})`,
            `(cn=${processedUsername})`,
            `(userPrincipalName=${processedUsername}*)`,
            `(displayName=*${processedUsername}*)`
          ];

          searchPaths.forEach((baseDN, pathIndex) => {
            searchFilters.forEach((filter, filterIndex) => {
              const searchOptions = {
                scope: 'sub',
                filter: filter,
                attributes: ['cn', 'sAMAccountName', 'mail', 'department', 'title', 'telephoneNumber', 'userPrincipalName', 'distinguishedName']
              };

              console.log(`[用户查找] 搜索路径 ${pathIndex + 1}/${searchPaths.length}, 过滤器 ${filterIndex + 1}/${searchFilters.length}: ${filter} 在 ${baseDN}`);

              client.search(baseDN, searchOptions, (err, searchRes) => {
                if (err) {
                  console.log(`[用户查找] 路径 ${baseDN} 搜索失败: ${err.message}`);
                } else {
                  searchRes.on('searchEntry', (entry) => {
                    const user = entry.object;
                    console.log(`[用户查找] 找到匹配用户!`);
                    console.log(`  DN: ${user.distinguishedName}`);
                    console.log(`  sAMAccountName: ${user.sAMAccountName}`);
                    console.log(`  CN: ${user.cn}`);
                    
                    results.push({
                      searchPath: baseDN,
                      searchFilter: filter,
                      user: {
                        dn: user.distinguishedName,
                        sAMAccountName: user.sAMAccountName,
                        cn: user.cn,
                        mail: user.mail,
                        department: user.department,
                        title: user.title
                      }
                    });
                  });

                  searchRes.on('error', (searchErr) => {
                    console.log(`[用户查找] 搜索过程出错 ${baseDN}: ${searchErr.message}`);
                  });

                  searchRes.on('end', () => {
                    // 搜索完成
                  });
                }

                completedSearches++;
                if (completedSearches === searchPaths.length * searchFilters.length) {
                  // 所有搜索完成
                  setTimeout(() => {
                    resolve(results);
                  }, 1000); // 等待1秒确保所有结果都收集到
                }
              });
            });
          });
        };

        performBind();
      });
    };

    const results = await findUser();
    await safeUnbind(client);

    res.json({
      success: true,
      username: processedUsername,
      searchResults: results,
      totalFound: results.length
    });

  } catch (error) {
    console.error(`[用户查找] 查找失败:`, error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// LDAP根DN发现API
app.get('/api/ldap/discover-root', async (req, res) => {
  const config = LDAP_CONFIG.primary;
  
  console.log(`[根DN发现] 开始发现LDAP根结构`);
  
  try {
    const client = createSafeClient({
      ...config,
      timeout: 10000,
      connectTimeout: 10000
    });

    const discoverRoot = () => {
      return new Promise((resolve, _reject) => {
        // 可能的根DN列表 - 扩展搜索范围
        const possibleRoots = [
          '', // 根目录
          // 基于您的配置尝试不同组合
          'DC=DEVVDI,DC=YNRCC,DC=COM',
          'DC=YNRCC,DC=COM', 
          'DC=COM',
          // 尝试不同的域结构
          'DC=devvdi,DC=ynrcc,DC=com', // 小写版本
          'DC=DEVVDI',
          'DC=YNRCC',
          // 常见的企业域名结构
          'DC=local',
          'DC=domain,DC=local',
          'DC=corp',
          'DC=company,DC=com',
          'DC=ad,DC=local',
          'DC=contoso,DC=com',
          // Windows默认域
          'DC=domain',
          'DC=test,DC=local'
        ];

        const results = [];
        let completedAttempts = 0;

        const performBind = () => {
          if (config.bindDN && config.bindPassword) {
            console.log(`[根DN发现] 尝试绑定: ${config.bindDN}`);
            client.bind(config.bindDN, config.bindPassword, (err) => {
              if (err) {
                console.log(`[根DN发现] 绑定失败: ${err.message}`);
                console.log(`[根DN发现] 尝试匿名绑定`);
                testRoots();
              } else {
                console.log(`[根DN发现] 绑定成功`);
                testRoots();
              }
            });
          } else {
            console.log(`[根DN发现] 使用匿名绑定`);
            testRoots();
          }
        };

        const testRoots = () => {
          possibleRoots.forEach((rootDN, index) => {
            const searchOptions = {
              scope: 'one',
              filter: '(objectClass=*)',
              attributes: ['cn', 'ou', 'dc', 'objectClass', 'distinguishedName']
            };

            const searchDN = rootDN || ''; // 空字符串表示根目录
            console.log(`[根DN发现] 测试根DN ${index + 1}/${possibleRoots.length}: "${searchDN}"`);

            client.search(searchDN, searchOptions, (err, searchRes) => {
              completedAttempts++;
              
              if (err) {
                console.log(`[根DN发现] 根DN "${searchDN}" 失败: ${err.message}`);
                results.push({
                  rootDN: searchDN,
                  success: false,
                  error: err.message,
                  entries: []
                });
              } else {
                const entries = [];
                let hasEntries = false;

                searchRes.on('searchEntry', (entry) => {
                  hasEntries = true;
                  const obj = entry.object;
                  console.log(`[根DN发现] 在 "${searchDN}" 找到条目: ${obj.distinguishedName}`);
                  entries.push({
                    dn: obj.distinguishedName,
                    cn: obj.cn,
                    ou: obj.ou,
                    dc: obj.dc,
                    objectClass: obj.objectClass
                  });
                });

                searchRes.on('error', (searchErr) => {
                  console.log(`[根DN发现] 搜索 "${searchDN}" 过程出错: ${searchErr.message}`);
                  results.push({
                    rootDN: searchDN,
                    success: false,
                    error: searchErr.message,
                    entries: []
                  });
                });

                searchRes.on('end', () => {
                  results.push({
                    rootDN: searchDN,
                    success: hasEntries,
                    error: hasEntries ? null : 'No entries found',
                    entries: entries
                  });
                  
                  if (hasEntries) {
                    console.log(`[根DN发现] ✅ 根DN "${searchDN}" 有效，找到 ${entries.length} 个条目`);
                  }
                });
              }

              // 所有尝试完成后返回结果
              if (completedAttempts === possibleRoots.length) {
                setTimeout(() => {
                  resolve(results);
                }, 1000);
              }
            });
          });
        };

        performBind();
      });
    };

    const results = await discoverRoot();
    await safeUnbind(client);

    const validRoots = results.filter(r => r.success);
    
    res.json({
      success: true,
      validRoots: validRoots,
      allAttempts: results,
      totalValidRoots: validRoots.length
    });

  } catch (error) {
    console.error(`[根DN发现] 发现失败:`, error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// LDAP目录结构探索API
app.get('/api/ldap/explore', async (req, res) => {
  const config = LDAP_CONFIG.primary;
  const { baseDN } = req.query;
  
  try {
    const client = createSafeClient(config);

    const exploreDirectory = () => {
      return new Promise((resolve, reject) => {
        // 绑定操作
        const performBind = () => {
          if (config.bindDN && config.bindPassword) {
            client.bind(config.bindDN, config.bindPassword, (err) => {
              if (err) {
                reject(new Error('LDAP绑定失败'));
                return;
              }
              searchDirectory();
            });
          } else {
            searchDirectory();
          }
        };

        // 搜索目录结构
        const searchDirectory = () => {
          const searchBaseDN = baseDN || config.baseDN;
          const searchOptions = {
            scope: 'one', // 只搜索直接子项
            filter: '(objectClass=*)',
            attributes: ['cn', 'ou', 'objectClass', 'distinguishedName']
          };

          console.log(`[目录探索] 搜索: ${searchBaseDN}`);

          client.search(searchBaseDN, searchOptions, (err, searchRes) => {
            if (err) {
              reject(new Error(`搜索失败: ${err.message}`));
              return;
            }

            const results = [];
            
            searchRes.on('searchEntry', (entry) => {
              const obj = entry.object;
              results.push({
                dn: obj.distinguishedName,
                cn: obj.cn,
                ou: obj.ou,
                objectClass: obj.objectClass
              });
            });

            searchRes.on('error', (searchErr) => {
              console.error(`[目录探索] 搜索错误: ${searchErr.message}`);
              reject(searchErr);
            });

            searchRes.on('end', () => {
              console.log(`[目录探索] 搜索完成，找到 ${results.length} 个条目`);
              resolve(results);
            });
          });
        };

        // 开始搜索
        performBind();
      });
    };

    const results = await exploreDirectory();
    await safeUnbind(client);

    res.json({
      success: true,
      results: results,
      totalCount: results.length
    });

  } catch (error) {
    console.error('[目录探索] 错误:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});


// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🔗 LDAP认证服务器已启动`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 当前环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🛑 按Ctrl+C停止服务
`);
});
