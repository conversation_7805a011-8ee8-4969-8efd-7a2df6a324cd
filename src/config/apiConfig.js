// YNNX AI Platform - 统一API配置
// 集中管理所有API地址，避免硬编码

/**
 * 环境检测 - 判断是否在Node.js环境中运行
 */
const isNodeEnvironment = () => {
  return typeof process !== 'undefined' && process.env && typeof import.meta === 'undefined' || !import.meta.env;
};

/**
 * 获取环境变量的兼容函数
 */
const getEnvVar = (key, fallback = undefined) => {
  if (isNodeEnvironment()) {
    return process.env[key] || fallback;
  } else {
    return import.meta.env[key] || fallback;
  }
};

/**
 * API配置管理
 * 优先级：环境变量 > 默认配置
 */
const API_CONFIG = {
  // 开发环境默认配置
  DEVELOPMENT: {
    LITELLM_API_BASE: getEnvVar('VITE_DEV_LITELLM_API_BASE', '/api/litellm'),
    LDAP_API_URL: getEnvVar('VITE_DEV_LDAP_API_URL', '/api/ldap')
  },
  
  // 生产环境默认配置（使用nginx代理路径）
  PRODUCTION: {
    LITELLM_API_BASE: getEnvVar('VITE_PROD_LITELLM_API_BASE', '/api/litellm'),
    LDAP_API_URL: getEnvVar('VITE_PROD_LDAP_API_URL', '/api/ldap')
  }
};

// 获取当前环境
const getEnvironment = () => {
  return getEnvVar('NODE_ENV', 'development');
};

// 获取API配置
const getApiConfig = () => {
  const env = getEnvironment();
  let defaults;
  
  // 根据环境选择合适的默认配置
  if (env === 'production') {
    defaults = API_CONFIG.PRODUCTION;
  } else if (env === 'test' || getEnvVar('VITE_API_MODE') === 'test') {
    defaults = API_CONFIG.TEST;
  } else {
    defaults = API_CONFIG.DEVELOPMENT;
  }
  
  // 确保使用相对路径，避免直接使用IP地址
  const ensureRelativePath = (url) => {
    if (url && url.startsWith('http')) {
      try {
        const urlObj = new URL(url);
        return urlObj.pathname.replace(/\/+$/, '');
      } catch (e) {
        return url;
      }
    }
    return url;
  };
  
  // 明确使用VITE_前缀的环境变量，避免被后端配置覆盖
  const litellmBase = getEnvVar('VITE_LITELLM_API_BASE') || defaults.LITELLM_API_BASE;
  const ldapUrl = getEnvVar('VITE_LDAP_API_URL') || defaults.LDAP_API_URL;
  
  return {
    LITELLM_API_BASE: ensureRelativePath(litellmBase),
    LDAP_API_URL: ensureRelativePath(ldapUrl)
  };
};

// 导出配置
export const API_ENDPOINTS = getApiConfig();

// 便捷方法
export const getLiteLLMApiBase = () => API_ENDPOINTS.LITELLM_API_BASE;
export const getLdapApiUrl = () => API_ENDPOINTS.LDAP_API_URL;

// 获取LiteLLM后端服务地址（用于显示给用户）
export const getLiteLLMBackendUrl = () => {
  return getEnvVar('LITELLM_BACKEND_URL') || getEnvVar('VITE_LITELLM_BACKEND_URL') || 'https://192.168.60.52:14000';
};

// 调试信息（仅开发环境）
if (!isNodeEnvironment() && getEnvVar('DEV')) {
  console.log('🔧 API配置信息:', {
    environment: getEnvironment(),
    endpoints: API_ENDPOINTS,
    isNodeEnv: isNodeEnvironment()
  });
} 